/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/7/17.
 */

import React, { useEffect, useState, useMemo } from 'react'
import styles from './AttachmentItem.module.less'
import { logPreviewAttachmentFile } from '../../../lib/logs'
import { app as api } from '@ekuaibao/whispered'
import {
  OutlinedDirectionDownload,
  OutlinedGeneralView,
  OutlinedEditDeleteTrash,
  OutlinedGeneralAttachment,
  OutlinedTipsMoreAdd,
  OutlinedTipsNo,
  OutlinedDirectionSwitch,
  OutlinedDirectionRotateRight,
  OutlinedDirectionRotateLeft,
  TwoToneGeneralAiSummary,
  OutlinedDirectionUp,
  OutlinedDirectionDown,
  TwoToneGeneralAutofill,
  OutlinedDirectionRefresh
} from '@hose/eui-icons'
import { Popconfirm, Tooltip, Ellipsis, Progress, Image, Space, Table } from '@hose/eui'
import { isObject } from 'lodash'
import { formatAIResultToString } from '../../../components/dynamic/parseAIResult'
import { getBoolVariation } from '../../../lib/featbit'
import classnames from 'classnames'

const IMG_REG_PRE = /^(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|png|raw|tga)$/i

const noop = () => { }

export default function AttachmentItem(props) {
  const {
    isEdit,
    isOCR,
    disable,
    file,
    onRemoveItem,
    onClickItem,
    deleteGpyResultList,
    onFilePreview,
    onFileDownload,
    style,
    onChangeIndex,
    useAI,
    onAIResult,
    onApplyAIResult,
    autoApplyAIResult,
  } = props

  const [AIPercent, setAIPercent] = useState(0)
  const [AIResult, setAIResult] = useState({})
  const [AIResultTypeMap, setAIResultTypeMap] = useState({})
  const [datalinkFieldMap, setDatalinkFieldMap] = useState({})
  const [paramFields, setParamFields] = useState([])
  const [isAIResultExpand, setIsAIResultExpand] = useState(true)
  const [AIProgressDescription, setAIProgressDescription] = useState('')

  const handleClick = () => onClickItem(file)

  const handleRemove = (e) => {
    e.stopPropagation()
    e.preventDefault()
    onRemoveItem?.(file)
    if (deleteGpyResultList) {
      deleteGpyResultList?.(file)
    }
    if (onChangeIndex) {
      onChangeIndex()
    }
  }

  const handleFilePreview = e => {
    e.stopPropagation()
    e.preventDefault()
    logPreviewAttachmentFile(file)
    onFilePreview?.(file)
  }

  const handleDownload = e => {
    e.stopPropagation()
    e.preventDefault()
    onFileDownload?.(file)
  }

  const fileCanBeDownload = () => {
    return api.getState('@common').fileCanDownload
  }

  let { key, fileName, name, status, progress } = file
  let percent = 100
  let isShowProgress = false
  if (status && status === 'uploading') {
    fileName = name
    percent = progress.percent
    isShowProgress = true
  }

  const handleAIResult = async (e) => {
    e.stopPropagation()
    e.preventDefault()

    // 重置AIPercent为0
    setAIPercent(1)

    // 创建更逼真的进度模拟，适应10-20秒的接口响应时间
    const simulateRealisticProgress = () => {
      // 随机生成总时长(10-20秒)
      const totalDuration = Math.floor(Math.random() * 10000) + 10000;

      // 定义进度阶段和相对比例
      const stages = [
        { targetPercent: 15, durationPercent: 12, desc: '正在分析文件...' },     // 快速起步阶段
        { targetPercent: 35, durationPercent: 20, desc: '提取文本内容...' },     // 文本提取阶段
        { targetPercent: 60, durationPercent: 25, desc: '识别关键信息...' },     // 识别阶段(最耗时)
        { targetPercent: 82, durationPercent: 28, desc: '处理数据结构...' },     // 处理阶段
        { targetPercent: 95, durationPercent: 15, desc: '准备结果数据...' }      // 准备结果阶段
      ];

      // 计算每个阶段的实际目标进度和持续时间
      let previousTarget = 1;
      const calculatedStages = stages.map(stage => {
        const target = stage.targetPercent;
        const duration = Math.floor((stage.durationPercent / 100) * totalDuration);
        const result = {
          target,
          duration,
          startValue: previousTarget,
          desc: stage.desc
        };
        previousTarget = target;
        return result;
      });

      console.log(`模拟进度总时长: ${totalDuration}ms`);

      let currentStage = 0;
      let currentProgress = 1;
      let stageStartTime = Date.now();
      let progressDescriptionCallback = null;

      // 如果有状态描述回调
      if (typeof setAIProgressDescription === 'function') {
        progressDescriptionCallback = setAIProgressDescription;
        progressDescriptionCallback(calculatedStages[0].desc);
      }

      // 创建定时器
      const interval = setInterval(() => {
        const now = Date.now();
        const stageElapsed = now - stageStartTime;

        if (currentStage >= calculatedStages.length) {
          clearInterval(interval);
          return;
        }

        const { target, duration, startValue } = calculatedStages[currentStage];

        // 计算该阶段的进度增量 - 使用缓动函数使进度更自然
        const stageProgress = Math.min(stageElapsed / duration, 1);
        // 使用缓动函数: 开始快，然后减慢
        const easedProgress = 1 - Math.pow(1 - stageProgress, 2);
        const targetProgress = startValue + (target - startValue) * easedProgress;

        // 添加微小随机波动，模拟网络不稳定性
        const randomFactor = 1 + (Math.random() * 0.06 - 0.03); // -3% 到 +3% 的随机波动

        // 计算新进度
        let newProgress = targetProgress * randomFactor;

        // 确保不超过当前阶段目标
        if (stageProgress >= 1) {
          newProgress = target;
          currentStage++;
          stageStartTime = now;

          // 偶尔在阶段之间添加一个短暂停顿，模拟处理延迟
          if (currentStage < calculatedStages.length && Math.random() > 0.6) {
            // 60%概率出现短暂停顿
            setTimeout(() => {
              stageStartTime = Date.now(); // 重置时间，消除停顿影响
            }, Math.random() * 200 + 100); // 100-300ms的随机停顿
          }

          // 更新进度描述
          if (progressDescriptionCallback && currentStage < calculatedStages.length) {
            progressDescriptionCallback(calculatedStages[currentStage].desc);
          }
        }

        // 更新进度
        currentProgress = Math.min(Math.floor(newProgress), target);
        setAIPercent(currentProgress);

      }, 100); // 每100ms更新一次，使动画更流畅

      return {
        interval,
        expectedDuration: totalDuration
      };
    };

    // 启动进度模拟
    const { interval: progressInterval, expectedDuration } = simulateRealisticProgress();

    // 设置超时保护，确保进度条不会卡在99%太久
    const timeoutProtection = setTimeout(() => {
      clearInterval(progressInterval);
      setAIPercent(99); // 强制设置为99%
    }, expectedDuration + 3000); // 预期时间+3秒的保护时间

    try {
      // 记录实际开始时间
      const apiStartTime = Date.now();
      // 等待API请求完成
      const { result, typeMap, datalinkFieldMap, fields } = await onAIResult?.(file);
      // 记录API实际耗时
      const apiDuration = Date.now() - apiStartTime;
      console.log(`API实际耗时: ${apiDuration}ms`);

      // 清除模拟进度和超时保护
      clearInterval(progressInterval);
      clearTimeout(timeoutProtection);

      // 确保进度是线性前进的，防止回退
      // 使用函数式更新确保基于最新状态
      setAIPercent(currentPercent => {
        // 如果当前进度已经在99%以上，就不要回退
        return Math.max(currentPercent, 95);
      });

      // 设置结果（预先处理，避免阻塞UI）
      const processedResult = await Promise.resolve().then(() => {
        setAIResultTypeMap(typeMap);
        setDatalinkFieldMap(datalinkFieldMap);
        setParamFields(fields);
        return result;
      });

      // 使用Promise和setTimeout链确保进度更新有序进行
      await new Promise(resolve => {
        // 先到99%
        setTimeout(() => {
          setAIPercent(99);
          resolve();
        }, 300);
      });

      await new Promise(resolve => {
        // 再到100%，增强完成感
        setTimeout(() => {
          setAIPercent(100);
          if (typeof setAIProgressDescription === 'function') {
            setAIProgressDescription('处理完成');
          }
          resolve();
        }, 200);
      });

      // 最后处理结果应用，放在单独的tick中执行
      setTimeout(() => {
        setAIResult(processedResult);

        if (isObject(processedResult) &&
          Object.keys(processedResult).length > 0 &&
          autoApplyAIResult) {
          applyAIResultToForm(e, processedResult, typeMap, datalinkFieldMap, fields);
        }
      }, 50);

    } catch (error) {
      console.error('AI处理出错:', error);

      clearInterval(progressInterval);
      clearTimeout(timeoutProtection);

      // 处理错误情况，显示错误进度
      if (typeof setAIProgressDescription === 'function') {
        setAIProgressDescription('处理失败');
      }
    }
  }

  const applyAIResultToForm = (e, result = AIResult, typeMap = AIResultTypeMap, fieldMap = datalinkFieldMap, fields = paramFields) => {
    e?.stopPropagation()
    e?.preventDefault()
    const res = onApplyAIResult?.(result, typeMap, fieldMap, fields)
    if (res?.retry) {
      setAIPercent(0)
      setAIResult({})
    }
  }

  const handleAIResultExpand = () => {
    setIsAIResultExpand(prev => !prev)
  }

  const aiNotDisabled = useMemo(() => {
    const fileNameExtension = file?.fileName?.split('.')?.pop() ?? ''
    return ['pdf', 'docx', 'doc', 'png', 'jpg', 'jpeg'].includes(fileNameExtension.toLowerCase())
  }, [file?.fileName])

  const progressDescription = useMemo(() => {
    if (AIPercent === -1) {
      return AIProgressDescription
    }
    return `${AIProgressDescription}（${AIPercent}%）`
  }, [AIPercent, AIProgressDescription])

  const hasFillFormButton = useMemo(() => {
    return isObject(AIResult) && Object.keys(AIResult).length > 0 && AIPercent === 100
  }, [AIResult, AIPercent])

  const hasAIButton = useMemo(() => {
    return useAI && AIPercent === 0
  }, [useAI, AIPercent])

  const hasAIResultContent = useMemo(() => {
    return useAI && AIPercent > 0
  }, [useAI, AIPercent])

  const showAIResultContent = useMemo(() => {
    return isObject(AIResult) && Object.keys(AIResult).length > 0 && isAIResultExpand
  }, [AIResult, isAIResultExpand])

  const showRefreshButton = useMemo(() => {
    return (AIResult === undefined || AIResult === null) && AIPercent === 100
  }, [AIResult, AIPercent])

  const AIResultContentIsNull = useMemo(() => {
    return isObject(AIResult) && Object.keys(AIResult).length === 0 && AIPercent === 100
  }, [AIResult, AIPercent])

  return (
    <div className={classnames(styles['attachment-item'], 'attachment-item-w', props.className)}>
      <div onClick={handleClick} className={styles['file-item']} style={style}>
        <Tooltip placement="top" title={fileName || name}>
          <div className="file-wrapper" onClick={handleFilePreview}>
            <PreImg file={file} />
            <div className="file-name form_item__content_forFix">
              <Ellipsis direction="middle" content={fileName || name} />
            </div>
          </div>
        </Tooltip>
        <div className="action-wrapper">
          {!isOCR && (
            <>
              {getBoolVariation('aprd-3778-ai-attachment', false) && (hasAIButton ? (
                <Tooltip title={aiNotDisabled ? null : i18n.get('仅支持pdf、docx、png、jpg、jpeg格式')}>
                  <span onClick={aiNotDisabled ? handleAIResult : null} className={aiNotDisabled ? "suport-data-ai-summary file-AI" : "suport-data-ai-summary file-AI-disabled"}>
                    <TwoToneGeneralAiSummary />
                    <span>AI 摘要</span>
                  </span>
                </Tooltip>
              ) : <span suport-data-ai-summary/>)}
              <span onClick={handleFilePreview} className="file-preview">
                <OutlinedGeneralView />
              </span>
              {key?.startsWith('DP:') ? null : fileCanBeDownload() ? (
                <span onClick={handleDownload} className="file-download">
                  <OutlinedDirectionDownload />
                </span>
              ) : (
                <Tooltip placement="topRight" title={i18n.get('您无权进行此操作，可联系管理员授权')}>
                  <span className="file-download not-allow">
                    <OutlinedDirectionDownload />
                  </span>
                </Tooltip>
              )}
            </>
          )}
          {isEdit && !disable && (
            <Popconfirm
              align={{ targetOffset: [-7] }}
              placement="topRight"
              arrowPointAtCenter
              autoAdjustOverflow
              title={i18n.get('确定删除附件？')}
              content={i18n.get('删除后无法撤回，请谨慎操作')}
              okText={i18n.get('删除')}
              okButtonProps={{ theme: 'danger' }}
              cancelText={i18n.get('取消')}
              onConfirm={handleRemove}
            >
              <div className="del-btn">
                <OutlinedEditDeleteTrash />
              </div>
            </Popconfirm>
          )}
        </div>
      </div>
      {hasAIResultContent && (
        <div className="file-AI-result">
          <div className="file-AI-result-title">
            <div className="file-AI-result-title-left">
              <div className="file-AI-result-title-left-title-wrapper">
                <TwoToneGeneralAiSummary fontSize={16} />
                <span className="file-AI-result-title-left-title">AI信息摘要</span>
              </div>
              {AIPercent < 100 && <span className="file-AI-result-title-left-percent">{progressDescription}</span>}
            </div>
            <div className="file-AI-result-title-right">
              {
                hasFillFormButton &&
                <Space size={4} className="file-AI-result-title-right-button" onClick={applyAIResultToForm}>
                  <TwoToneGeneralAutofill fontSize={14} />
                  <span className="file-AI-result-title-right-button-text">一键填单</span>
                </Space>
              }
            </div>
          </div>
          {showAIResultContent && (<>
            <div className="file-AI-result-content">
              <Space direction="vertical" size={2}>
                {
                  Object.keys(AIResult).map((key) => {
                    const keyType = AIResultTypeMap[key]
                    const dataSource = Array.isArray(AIResult[key]) ? AIResult[key].filter(item => Object.values(item).filter(Boolean).length > 0) : []
                    const columns = Array.isArray(datalinkFieldMap[key]) ? datalinkFieldMap[key].map(item => ({
                      title: item.label,
                      dataIndex: item.label,
                      key: item.label,
                      render: (_, record) => {
                        return  <span className="file-AI-result-content-value">{formatAIResultToString(record, item.label, {[item.label]:item.type})}</span>
                      }
                    })) : []

                    if (keyType === 'list:dataLinkEdits' && dataSource) {
                      return <div key={key} className={`file-AI-result-content-item-dom`}>
                        <div className="file-AI-result-content-key">{key}：</div>
                        <div className="file-AI-result-content-value">
                          <Table
                            columns={columns}
                            dataSource={dataSource}
                            pagination={false}
                            bordered
                            size="small"
                            className='file-AI-result-table'
                          />
                        </div>
                      </div>
                    }
                    return <div key={key} className={`file-AI-result-content-item`}>
                      <span className="file-AI-result-content-key">{key}：</span>
                      <span className="file-AI-result-content-value">{formatAIResultToString(AIResult, key, AIResultTypeMap) || '无内容'}</span>
                    </div>
                  })
                }
              </Space>
            </div>
            <div className="file-AI-result-content-expand">
              <span className="ai-result-tip">{i18n.get('内容由AI生成')}</span>
              <Space size={4} className="file-AI-result-content-expand-text">
                <OutlinedDirectionUp fontSize={14} />
                <span onClick={handleAIResultExpand}>收起</span>
              </Space>
            </div>
          </>
          )}
          {
            showRefreshButton && (
              <div className="file-AI-result-content">
                <div key={key} className={`file-AI-result-content-item collapse-wrapper`}>
                  <span className="file-AI-result-content-key">{i18n.get('AI 提取失败，请重试')}</span>
                  <Space size={4} className="file-AI-result-content-collapse-text">
                    <OutlinedDirectionRefresh fontSize={14} />
                    <span onClick={handleAIResult}>刷新重试</span>
                  </Space>
                </div>
              </div>
            )
          }
          {
            AIResultContentIsNull && (
              <div className="file-AI-result-content">
                <div key={key} className={`file-AI-result-content-item collapse-wrapper`}>
                  <span className="file-AI-result-content-key">{i18n.get('未提取到字段信息')}</span>
                </div>
              </div>
            )
          }
          {!isAIResultExpand && (
            <>
              {
                isObject(AIResult) && Object.keys(AIResult).slice(0, 1).map((key) => {
                  return <div key={key} className={`file-AI-result-content-item collapse-wrapper`}>
                    <span className="file-AI-result-content-key">{key}：</span>
                    {typeof AIResult[key] !== 'object' && (
                      <span className="file-AI-result-content-value">{AIResult[key]?.toString()}...</span>
                    )}
                    <Space size={4} className="file-AI-result-content-collapse-text">
                      <OutlinedDirectionDown fontSize={14} />
                      <span onClick={handleAIResultExpand}>展开</span>
                    </Space>
                  </div>
                })
              }
            </>
          )}
        </div>
      )}
      {isEdit && isShowProgress && <Progress size="small" percent={percent} showInfo={false} />}
    </div>
  )
}

export const ImagePreview = props => {
  const { open, current: defaultCurrent, images = [], onVisibleChange, onFileDownload } = props
  const [visible, setVisible] = useState(false)
  const [current, setCurrent] = useState(defaultCurrent)

  useEffect(() => {
    if (open) {
      setVisible(true)
    }
  }, [open])

  useEffect(() => {
    setCurrent(defaultCurrent)
  }, [defaultCurrent])

  const handleImageChange = (current, prev) => {
    setCurrent(current)
  }

  const handleOnVisibleChange = vis => {
    onVisibleChange(vis)
    setVisible(vis)
  }

  return (
    <div style={{ display: 'none' }}>
      <Image.PreviewGroup
        preview={{
          maskClosable: false,
          visible,
          current,
          onVisibleChange: handleOnVisibleChange,
          onChange: (current, prev) => handleImageChange(current, prev),
          toolbarRender: (
            _,
            { transform: { scale }, actions: { onFlipY, onFlipX, onRotateLeft, onRotateRight, onZoomOut, onZoomIn } }
          ) => (
            <Space size={0} className={styles['attachment-toolbar-wrapper']}>
              <OutlinedDirectionSwitch className="action" rotate={90} onClick={onFlipY} />
              <OutlinedDirectionSwitch className="action" onClick={onFlipX} />
              <OutlinedDirectionRotateLeft className="action" onClick={onRotateLeft} />
              <OutlinedDirectionRotateRight className="action" onClick={onRotateRight} />
              <OutlinedTipsNo className={`${scale === 1 ? 'action disabled' : 'action'}`} onClick={onZoomOut} />
              <OutlinedTipsMoreAdd className={`${scale === 50 ? 'action disabled' : 'action'}`} onClick={onZoomIn} />
              <OutlinedDirectionDownload className="action" onClick={() => onFileDownload(images[current])} />
            </Space>
          )
        }}
      >
        {images.map(img => {
          return <Image key={img.fileId} src={img.url} />
        })}
      </Image.PreviewGroup>
    </div>
  )
}

AttachmentItem.defaultProps = {
  isEdit: false,
  file: {
    key: undefined,
    fileId: '',
    fileName: '',
    name: '',
    url: '',
    id: '',
    thumbUrl: ''
  },
  onRemoveItem: noop,
  onClickItem: noop
}


function PreImg(props) {
  const { file } = props

  const icon = (
    <OutlinedGeneralAttachment className="file-img" fontSize={20} style={{ color: 'var(--eui-icon-n2)' }} />
  )
  if (file.key && file.key.startsWith('DP:')) {
    return icon
  }
  const img = file.thumbUrl || file.url
  return IS_STANDALONE ? icon : (IMG_REG_PRE.test(file.fileName) && img) ? <img className="file-img" src={img} /> : icon
}