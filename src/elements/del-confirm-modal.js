import './del-confirm-modal-style.less'
import { Form } from 'antd'
import { Button, Input } from '@hose/eui'
import { OutlinedTipsClose, FilledTipsWarning } from '@hose/eui-icons'
import React from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import EnhanceFormCreate from './enhance/enhance-form-create'
const FormItem = Form.Item

@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer'
})
@EnhanceFormCreate()
export default class DelConfirmModal extends React.Component {
  constructor(props) {
    super(props)
    props.overrideGetResult(this.getResult)
  }

  //  标准输入格式 示例
  //  this.props.data = {
  //    title : '删除该流程',                    // 删除的title
  //    name : '日常审批流',                     // 要删除的名称 * 必填
  //    placeholder : '输入流程名称以确认删除',   // Input的placeholder
  //    required : '流程名称不能为空',           // Input 为空时的提示
  //    warning : '删除操作无法撤销。',          // 警告标志后的提示语
  //    callback : '输入流程名称不正确'          // 校验失败后返回的callback
  //  }

  getResult = () => {
    return {}
  }

  /**
   * 标准化字符串，处理不同类型的空格字符
   * @param {string} str - 需要标准化的字符串
   * @returns {string} 标准化后的字符串
   */
  normalizeString = str => {
    if (!str) return ''
    // 将所有类型的空格字符（包括不间断空格 \u00A0）替换为普通空格，然后去除前后空格
    return str
      .replace(/[\s\u00A0\u2000-\u200B\u2028\u2029\u202F\u205F\u3000\uFEFF]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
  }

  checkName = (rule, value, callback) => {
    // 标准化处理后进行比较
    const normalizedValue = this.normalizeString(value)
    const normalizedTargetName = this.normalizeString(this.props.data.name)

    if (value && normalizedValue !== normalizedTargetName) {
      return callback(this.props.data.callback)
    }
    callback()
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
    this.props.form.resetFields()
  }

  handleModalSave = () => {
    const { validateFieldsAndScroll, resetFields } = this.props.form
    validateFieldsAndScroll((errors, values) => {
      if (!!errors) return
      this.props.layer.emitOk()
      resetFields()
    })
  }

  render() {
    const { getFieldDecorator } = this.props.form
    return (
      <div className="del-confirm-modal">
        <div className="modal-header">
          <div className="flex" />
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose} />
        </div>
        <div className="content center">
          <div className="title">
            {this.props.title || i18n.get('删除')}
            {i18n.get('：')}
            {this.props.data.name}
          </div>
          <Form>
            <FormItem style={{ width: 320 }}>
              {getFieldDecorator('name', {
                initialValue: '',
                rules: [
                  { required: true, whitespace: true, message: this.props.required || i18n.get('不能为空') },
                  { validator: this.checkName }
                ]
              })(<Input data-cy="deleteBudgetInp" placeholder={this.props.placeholder || i18n.get('请输入名称')} />)}
            </FormItem>
          </Form>
          {!this.props.notShowWarning && (
            <div className="desc horizontal">
              <FilledTipsWarning className="desc-icon" />
              <div>{this.props.warning || i18n.get('删除操作无法撤销。')}</div>
            </div>
          )}
        </div>
        <div className="modal-footer">
          <Button category="secondary" className="btn-ml" data-cy="delBudgetCancle" onClick={this.handleModalClose}>
            {i18n.get('取消')}
          </Button>
          <Button category="primary" className="btn-ml" data-cy="delBudgetConfirmBtn" onClick={this.handleModalSave}>
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
