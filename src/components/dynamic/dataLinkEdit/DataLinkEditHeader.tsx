import React, { FC, useState } from 'react'
import { Menu, Dropdown } from 'antd'
import fnGetFieldLabel from '../../utils/fnGetFieldLabel'
import styles from './DataLinkEditHeader.module.less'
import FormItemLabelWrap from '../../layout/FormItemLabelWrap/FormItemLabelWrap'
import { realName } from '../../../elements/util'
import { getBoolVariation } from '../../../lib/featbit'
import { addAiStyles } from '../../../plugins/bills/parts/right-part/billMore/flowTools'
import { getAiFilledFields } from '../../layout/FormItem'
import { AITextIcon } from '../../../elements/puppet/details/AITextIcon'
import { isValueChange } from '../../layout/FormWrapper'

const MenuItem = Menu.Item
interface IProps {
  field: any
  isEdit: boolean
  templates?: any[]
  dataLinkTemplateId?: string
  onChangeDataLinkTemplateId?: (id: string) => void
  clearButton?: React.ReactNode | null
  form?: any
  flowId?: string
  external?: boolean
  isForbid?: boolean
}

const DataLinkEditHeader: FC<IProps> = props => {
  const { field, isEdit, templates = [], dataLinkTemplateId = '', onChangeDataLinkTemplateId, clearButton, external, flowId, isForbid, form } = props
  const [visible, setVisible] = useState(false)
  const handleVisibleChange = show => {
    setVisible(show)
  }
  const handleMenuClick = selectKey => {
    onChangeDataLinkTemplateId?.(selectKey.key)
    setVisible(false)
  }
  const menu = (
    <Menu onClick={handleMenuClick}>
      {templates.map(item => (
        <MenuItem key={item.templateId}>{realName({ name: item.entity.name, enName: item.entity.enName })}</MenuItem>
      ))}
    </Menu>
  )

  const dropdownName = () => {
    const entity = templates.find(item => item.templateId === dataLinkTemplateId)?.entity
    return realName({ name: entity?.name, enName: entity.enName })
  }

  let aiFilledFields = {}
  if (form && flowId) {
    const showAiIcon = !isValueChange(form, flowId, field?.field)
    if (showAiIcon && getBoolVariation('aprd-5394-ai-chat')) {
      addAiStyles()
      aiFilledFields = getAiFilledFields(flowId)
    }
  }

  return (
    <div className={styles.header}>
      <div className={styles.title}>
        {!(field?.optional || !isEdit) && (
          <span style={{ color: '#ff7c7c', height: '20px', marginRight: '4px' }}>*</span>
        )}
        <FormItemLabelWrap
          isForbid={isForbid}
          field={field}
          flowId={flowId}
          isDetail={false}
          isEdit={false}
          external={external}
        >
          <span>{fnGetFieldLabel(field)}</span>
          {
            aiFilledFields[field?.field] && <span id={`${field?.field}-ai-icon`} className="ai-icon filled-ai-icon">
              <AITextIcon />
            </span>
          }
        </FormItemLabelWrap>
        {clearButton}
      </div>
      <div>
        {templates.length > 1 && (
          <Dropdown overlay={menu} onVisibleChange={handleVisibleChange} visible={visible}>
            <span>{dropdownName()}</span>
          </Dropdown>
        )}
      </div>
    </div>
  )
}

export default DataLinkEditHeader
