/**************************************
 * Created By LinK On 2021/12/10 15:23.
 **************************************/
import filtersFixer from '../../../lib/filtersFixer'
import parseQuery2Select from '@ekuaibao/lib/lib/parseQuery2Select'
import parseSelectUsePropertySet from '@ekuaibao/lib/lib/parseSelectUsePropertySet'
import { app as api } from '@ekuaibao/whispered'
import { Resource } from '@ekuaibao/fetch'
import { filterSystemBillStr } from '../bills.action'

const search = new Resource('/api/flow/v1/flows/my')

function _getPropertySet() {
  // @ts-ignore
  return api.getState('@common.globalFields.data') || []
}

// 搜索我的单据
export const searchMyBill = (params, scene, dimensionItems) => {
  const sceneFiltersQuery = scene ? filtersFixer(scene, 'flow', dimensionItems) : ''
  const sortersKeys = Object.keys(params?.sorters || {})
  if (sortersKeys.length === 0 || (sortersKeys.length === 1 && sortersKeys[0] === 'state')) {
    params.sorters = {
      ...params?.sorters || {},
      'form.submitDate': 'desc'
    }
  }
  const query = parseQuery2Select(params, undefined, 'flow')
  const content = parseSelectUsePropertySet(_getPropertySet(), params.options)
  query
    .filterBy('active == true')
    // .desc('createTime')
    .filterBy(sceneFiltersQuery)
    .select(`ownerId(id,name,enName),calcRiskWarning(...),form(${content ? content + ',' : ''}...),...`)
  query.filterBy(filterSystemBillStr())

  return search
    .POST(
      '',
      query.value(),
      {
        join: 'id,nodeState,/flow/v2/backlogs/state'
      },
      null,
      { hiddenLoading: true }
    )
    .then(data => {
      const { count, items } = data
      // 只记录了第一页的数据，后续有需求可再累加
      if (params?.page?.currentPage === 1) {
        api.invokeService('@bills:save:get:my:bill:TableList', items)
      }
      return { dataSource: items, total: count }
    })
}
