import { cloneDeep, sortBy, get, isObject, xor, isEqual } from 'lodash'
import { Fetch, Resource } from '@ekuaibao/fetch'
import { app as api } from '@ekuaibao/whispered'
import { getNodeValueByPath } from '@ekuaibao/lib/lib/lib-util'
import { getV } from '@ekuaibao/lib/lib/help'
import { showModal, showMessage } from '@ekuaibao/show-util'
import { T } from '@ekuaibao/i18n'
import { MoneyMath } from '@ekuaibao/money-math'
import Big from 'big.js'
import moment from 'moment'
import { IDENTIFY_INVOICE_TYPE } from '../../../lib/enums'
import { INVOICE_TYPE } from '@ekuaibao/lib/lib/enums'
import { InvoiceEntityType } from './types'
import { Modal } from '@hose/eui'
import { standardCurrencyToForeign, standardValueMoney } from '../../../lib/misc'
import { getAllApportionSpecification } from './billFetchUtil'
import { getSpecificationName } from '../../../lib/lib-util'
import qs from 'qs'
import {
  getBillTemplateAddFeeReceivingAmount,
  clearDetailsReceivingAmount,
  getBillReceivingCurrency
} from '../../../lib/fee-util'
import { getPayConfig } from './parse'
import { enableHidingFinishedBills } from '../../../lib/featbit/feat-switch'
import { getBoolVariation } from '../../../lib/featbit'
export { getSpecificationName }

const feeTypesVisibility = new Resource('/api/flow/v2/flows/feeType/visibility')
const specifications = new Resource('/api/form/v2/specifications')

export function getFeeTypeVisibleList(param) {
  return feeTypesVisibility.GET('', param)
}

//删除单据上的批注
export function deleteNoteById(params) {
  return Fetch.DELETE(`/api/credit/v2/notes/$${params.noteId}`, null, { body: { reason: params.reason } }).then(() => {
    setTimeout(() => api.invokeService('@bills:get:bill:notes', { flowId: params.flowId }))
  })
}

//获取申请事项中的预算
export function getRequitistionEvent(id) {
  return Fetch.GET(`/api/form/v2/requisition/$${id}/budgetoccupieds`)
}

//获取当前props所使用的SpecificationList
export function getSpecificationsByType(props, type) {
  return props[`${type === 'permit' ? 'requisition' : type}SpecificationList`] || []
}

//获取默认Specification
export function getDefSpecificationDS(dataSource, specificationGroupsList) {
  if (!specificationGroupsList) return
  if (dataSource.form && dataSource.form.specificationId) {
    let specification = dataSource.form.specificationId
    return specification
  }
  return null
}

//endregion
export function calculateFlowNodes(nodes = []) {
  let curNodes = cloneDeep(nodes)
  curNodes.forEach(o => {
    if (o.type === 'countersign') {
      const crossCorpApprove = getV(o, 'config.crossCorpApprove')
      if (crossCorpApprove) {
        o.counterSigners.forEach(k => {
          k.signerId = k.id
          delete k.id
        })
      } else {
        o.counterSigners.forEach(k => {
          k.signerId = k.signerId.id
        })
      }
      o.counterSignersCandidate.forEach(k => {
        k.signerId = k.signerId.id
      })
    }
  })
  return curNodes
}

//消费明细按照时间排序。规则是按照消费日期升序，如果没有消费日期，则取起止日期的开始日期。如果前两者均没有，则按照添加顺序排在列表底部
export function sortFeeTypeForm(details = [], orderByType = 'INPUT') {
  if (orderByType === 'INPUT') {
    if (details && details.length > 1) {
      return sortBy(details, [
        el => {
          return el.feeTypeForm.feeDate || (el.feeTypeForm.feeDatePeriod && el.feeTypeForm.feeDatePeriod.start)
        }
      ])
    }
  }
  return details
}

//弹出绑定窗口
export function showModalForFollowWeChat() {
  const KA_LOCAL_CONFIG = api.getState()['@common'].powers.KA_LOCAL_CONFIG
  if (KA_LOCAL_CONFIG) {
    return Fetch.GET('/api/v1/organization/corporations/select').then(resp => {
      const { value: { enableWechatBind = true } = {} } = resp
      if (!enableWechatBind) {
        return null
      }
      return doShowModalForFollowWeChat()
    })
  } else {
    return doShowModalForFollowWeChat()
  }
}

function doShowModalForFollowWeChat() {
  const disablePlatform = ['HUAWEI', 'FEISHU']
  //判断是否应该弹出绑定窗口
  return Fetch.GET('/api/weixin/v1/qrcode', { tagId: 104 }).then(res => {
    if (res.isBinding) return null
    const userInfo = api.getState('@common.userinfo.data')
    let isHaveLocalStorage = localStorage.getItem(
      'isBindWeChat:' + userInfo.staff.id + ' - ' + userInfo.staff.corporationId
    )
    if (
      !isHaveLocalStorage &&
      !disablePlatform.includes(window.__PLANTFORM__) &&
      Fetch.ekbCorpId !== 'DZgaDKv4Yk8400' &&
      res?.qrCode
    ) {
      // 判断该用户是否首次登陆 并且不为华为和飞书
      api.open('@bills:FollowWeChatModal', { qrCode: 'data:image/jpg;base64,' + res.qrCode })
    }
  })
}

export { getDetailCalculateMoney } from './getDetailCalculateMoney'
export { formatNewTemplateValue, formatExpenseLinkInfo } from './formatNewTemplateValue'
import { related } from '../../../elements/feeDetailViewList/Related'
import { uuid, isArray } from '@ekuaibao/helpers'
export { fnCheckPrintAvailable } from './fnCheckPrintAvailable'
import {
  batchGetSpecificationHiddenFieldsByIds,
  getDetailFLowRelation,
  getSpecificationHiddenFieldsById
} from '../bills.action'
import { createBillFromRecordNote } from './trackLogs'

export function setRemunerationBatchtoValue(dataSource, v, remunerationBatchField) {
  const remunerationBatch = get(dataSource, `form.${remunerationBatchField}`)
  if (remunerationBatch) {
    v[remunerationBatchField] = remunerationBatch
  }
}

export function renderInvoiceRiskTips(data) {
  let tips = []
  data.form.details &&
    data.form.details.find((item, index) => {
      if (item.feeTypeForm.invoiceForm && item.feeTypeForm.invoiceForm.type === 'wait') {
        return tips.push(
          <div
            key={index}
            style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}
          >
            <T name="有「待开发票」的消费" />
          </div>
        )
      }
    })

  return tips
}

export function getFlowRequirdNode(flows = []) {
  const budgetNodes = []
  let requiredNodes = []
  flows.forEach((item, index) => {
    const { conditionalType, type } = item
    //找出第预算超额需要审批的节点
    if (conditionalType === 'BUDGET') {
      budgetNodes.push({ budgetNode: item, budgetNodeIndex: index })
    }

    //找出必填节点
    let otherCheck = index === 0
    if (!requiredNodes.length) {
      otherCheck = true
    }
    if (type === 'branch') {
      otherCheck = false
    }
    if (checkPropty(item, otherCheck)) {
      requiredNodes.push({ requireNode: item, requireNodeIndex: index })
    }
  })

  const budgetNodeInfo = budgetNodes[0]
  const { budgetNode, budgetNodeIndex } = budgetNodeInfo || {}

  const requiredNodeInfo = requiredNodes[0]
  const { requireNode, requireNodeIndex = 0 } = requiredNodeInfo || {}

  requiredNodes = requiredNodes.map(item => item.requireNode)

  //有预算超额节点时：如果必填节点在预算超额前面或者在预算节点的下一个节点就不再找必填节点
  if (
    !!budgetNode && //存在预算超额节点
    requiredNodeInfo && //有必填节点
    requireNodeIndex >= budgetNodeIndex &&
    requireNodeIndex - budgetNodeIndex !== 1
  ) {
    //必填节点在预算超额后边并且不是预算节点的下一个节点
    //当地一个节点不是预算超额节点时，将下一个节点设置为必填节点
    for (let i = budgetNodeIndex + 1; i < flows.length; i++) {
      const node = flows[i]
      const { conditionalType } = node
      if (conditionalType !== 'BUDGET' && node.type !== 'branch' && checkPropty(node, true)) {
        requiredNodes.push(node)
        break
      }
    }
  }
  if (budgetNodes.length > 0) {
    const nodes = getFirstNoBudgetNodes(flows)
    const nodeList = nodes.filter(oo => oo.type !== 'branch')
    if (nodeList.length > 0) {
      requiredNodes = requiredNodes.concat(nodes)
    }
  }
  return requiredNodes
    .filter(item => !!item)
    .filter(item => item.type !== 'ebot' && item.type !== 'invoicingApplication' && item.type !== 'aiApproval')
    .map(item => item.configNodeId)
}

// 处理 第一个非预算节点以及以上节点必须是必填的
function getFirstNoBudgetNodes(flows) {
  const nodes = []
  let firstBudget = 0
  for (let i = 0; i < flows.length; i++) {
    const { conditionalType, type } = flows[i]
    if (conditionalType !== 'BUDGET' && type !== 'countersign' && type !== 'branch') {
      firstBudget = i
      break
    }
  }

  for (let i = 0; i <= firstBudget; i++) {
    const { skippedType, type } = flows[i]
    if (skippedType === 'NO_SKIPPED' && type !== 'countersign' && type !== 'branch') {
      nodes.push(flows[i])
    }
  }
  return nodes
}

function checkPropty(node, otherCheck = false) {
  const { config = {}, skippedType, type, isAllStaffs, staffIds } = node
  const isEbot = type === 'ebot' || type === 'invoicingApplication' || type === 'aiApproval'
  const hasApprover = isAllStaffs || (staffIds && staffIds.length > 0)
  const isPass = skippedType === 'NO_ABILITY' || skippedType === 'PAY_AMOUNT_IS_0' || !hasApprover || isEbot

  const isAbility = skippedType === 'NO_ABILITY'
  const isCountersign = type === 'countersign'

  let isSubmitterChoice = false,
    isManualChoose = true,
    isAuto = false
  if (!!config) {
    //由“提交人”指定： 手动选择时 必填 ；自动选择时 有可选审批人时必选
    isManualChoose = !config?.staffSelectionChoiceMode || ['ALL', 'RANGE'].includes(config?.staffSelectionChoiceMode)
    isSubmitterChoice = config?.isSubmitterChoice ?? false
    isAuto = config?.isAuto ?? isAuto
  }

  return (
    (otherCheck && ((isCountersign && !isAuto) || (!isCountersign && !isPass))) ||
    (!isAbility && isSubmitterChoice && (isManualChoose || hasApprover))
  )
}

export function confirmCopy(dataSource) {
  return new Promise(resolve => {
    const formType = get(dataSource, 'form.specificationId.type', '')
    if (formType === 'expense' || formType === 'requisition') {
      showModal.confirm({
        className: 'confirmCopyModal-wrapper',
        title: i18n.get('提示'),
        content:
          formType === 'expense'
            ? i18n.get(
                '复制生成的报销单，将不会携带原消费记录中的卡片及拆分组件的明细信息，如：发票、滴滴、合思等第三方卡片信息。如有疑问请找小易咨询'
              )
            : i18n.get('复制生成的申请单，将不会携带拆分组件的明细信息。如有疑问请找小易咨询复制生成的申请单'),
        okText: i18n.get('确认'),
        cancelText: i18n.get('取消'),
        onOk: () => {
          related.clearRelatedData()
          resolve()
        }
      })
    } else {
      resolve()
    }
  })
}

export async function formatCopyBillData(dataSource) {
  const form = get(dataSource, 'form')
  const data = await formatFormForCopy(form)
  return { state: 'new', formType: dataSource.formType, form: data, isCopy: true }
}

//复制单据时的过滤方法
export async function formatFormForCopy(value) {
  const delKeys = [
    'expenseLink',
    'expenseLinks',
    'linkRequisitionInfo',
    'writtenOffMoney',
    'expenseDate',
    'submitDate',
    'requisitionDate',
    'loanDate',
    'repaymentDate',
    'alterFlag'
  ]
  const { specificationId: specification } = value
  const components = get(specification, 'components', [])
  const dateCompList = components
    .filter(
      item =>
        item?.type === 'date' &&
        item?.defaultValue?.type === 'predefine' &&
        item?.defaultValue?.value === 'submit.date' &&
        item?.editable === false
    )
    .map(item => item.field) //查找所有日期组件是系统计算，并且设置默认值是当前日期的情况,从form中删除对应的value，组件重新赋值
  value = deleteKeys(value, specification, [...delKeys, ...dateCompList])
  // value = await checkSelectValue(value)
  let allCurrencyRate = []
  if (value?.legalEntityMultiCurrency && value?.legalEntityMultiCurrency?.form?.baseCurrencyId) {
    const { baseCurrencyId } = value?.legalEntityMultiCurrency?.form
    const { items: rates = [] } = await api.invokeService('@currency-manage:get:currency:rates:by:Id', baseCurrencyId)
    allCurrencyRate = rates
  } else {
    allCurrencyRate = await api.dataLoader('@common.allCurrencyRates').load()
  }

  const travelPlanning = value?.travelPlanning || []
  if (travelPlanning.length) {
    travelPlanning.forEach(el => {
      el.travelId = null
    })
  }

  const detailC = components.find(item => ['details', 'trips'].includes(item.field))
  const hiddenFields = await getSpecificationHiddenFields(specification)
  const hide = hiddenFields.includes(detailC?.field)
  const tempDataLinkIds = []
  if (hide) delete value.details
  // 更新支付计划的id
  if (value.payPlan && value.payPlan.length) {
    value.payPlan = value.payPlan.map(el => {
      el.dataLinkId = uuid(14)
      tempDataLinkIds.push(el.dataLinkId)
      if (!value?.paymentPlanByApportion && el.dataLinkForm?.E_system_支付计划_legalEntity) {
        delete el.dataLinkForm.E_system_支付计划_legalEntity
      }
      return el
    })
  }
  const invoiceType = get(value, 'invoiceForm.type')
  if (invoiceType === 'exist') {
    const invoiceFormField = components.find(el => el.field === 'invoiceForm')
    const { editable, defaultValue } = invoiceFormField
    if (!editable) {
      value.invoiceForm = { type: defaultValue.value }
    } else {
      value.invoiceForm = { type: invoiceType }
    }

    const taxs = components.filter(line => get(line, 'defaultValue.type') === 'invoiceSum').map(line => line.field)
    taxs.forEach(key => {
      delete value[key]
    })
  }
  if (!!detailC && !hide) {
    let details = get(value, `${detailC.field}`, [])
    const isSyncFeetypeId = tempDataLinkIds.length === details.length
    //费用明细字段处理
    details = details.map((item, idx) => {
      let detailForm = null
      const spe = get(item, 'specificationId')
      if (detailC.field === 'details') {
        item.feeTypeForm.detailId = isSyncFeetypeId ? tempDataLinkIds[idx] : uuid(14)
        // 复制时删除分摊id
        const apportions = getV(item, 'feeTypeForm.apportions', [])
        if (apportions.length) {
          apportions.forEach(el => {
            el.apportionForm['apportionId'] = 'ID_' + uuid(11)
          })
        }
        const travelPlanning = getV(item, 'feeTypeForm.travelPlanning', [])
        if (travelPlanning.length) {
          travelPlanning.forEach(el => {
            el.travelId = null
          })
        }
        detailForm = get(item, 'feeTypeForm')
        item.feeTypeForm = deleteKeys(detailForm, spe)
        item = formatDetailValue(item)
      } else {
        delete item.tripForm.tripId
        detailForm = get(item, 'tripForm')
        item.tripForm = deleteKeys(detailForm, spe)
      }
      return formatFormAmount(item, allCurrencyRate, 'feeTypeForm')
    })
    value[detailC.field] = details
  }
  return formatFormAmount(value, allCurrencyRate)
}

/**
 * 对数据中的金额字段格式化，过滤出所有金额字段
 * @param data 需要处理的数据
 * @param allCurrency: 系统中所有已配置的外币币种
 * @param valuePath: 对应的数据路径，默认传进来的data就是要处理的数据
 * @returns {*}
 */
function formatFormAmount(data, allCurrency, valuePath = '') {
  const components = getV(data, 'specificationId.components', [])
  if (!components.length) return data
  const moneyComponents = components.filter(item => {
    const type = getV(item, 'type', '')
    return type === 'money'
  })
  if (!moneyComponents.length) return data
  let value = !!valuePath ? data[valuePath] : data
  moneyComponents.forEach(item => {
    const field = getV(item, 'field', '')
    value[field] = formatAmount(value[field], allCurrency)
  })
  !!valuePath ? (data[valuePath] = value) : (data = value)
  return data
}

/**
 * 格式化金额：复制过来的金额中的税率和系统配置的外币的税率不一样的时候，按照系统配置的税率重新计算此金额中对应的本位币金额
 * @param amount 需要格式化的币种
 * @param allCurrency 系统中所有已配置的外币币种
 * @returns {*}
 */
function formatAmount(amount, allCurrency) {
  const foreignStrCode = getV(amount, 'foreignStrCode', '')
  const rate = getV(amount, 'rate', '')
  if (!foreignStrCode || !rate) return amount
  if (!allCurrency || !allCurrency.length) return amount
  const currency = allCurrency.find(item => item.strCode === foreignStrCode)
  if (!currency) return amount
  const currencyRate = getV(currency, 'rate', '')
  if (rate === currencyRate) return amount
  const foreign = getV(amount, 'foreign', 0)
  const standardScale = getV(amount, 'standardScale', 2)
  const result = { ...amount }
  result.rate = currencyRate
  result.sysRate = currencyRate
  result.standard = new Big(foreign).times(currencyRate).toFixed(Number(standardScale))
  if (amount.budget !== undefined) {
    const budgetRate = Number(amount.budgetRate) ? Number(amount.budgetRate) : 1
    result.budget = new Big(result.standard).div(budgetRate).toFixed(Number(amount.budgetScale) || 2)
  }
  return result
}
// 复制单据功能检查 检查自定义档案数据数据，如果是非叶子节点到话，就删除该字段
// 因遵守尽量不更改用户自己所选择操作，所以此方法注释，在保存单据的时候提示用户
async function checkSelectValue(value) {
  const components = getV(value, 'specificationId.components', [])
  const globalFields = api.getState()['@common'].globalFields.data || []
  const DimensionData = globalFields.filter(type => {
    const entity = getV(type, 'dataType.entity') || getV(type, 'dataType.elemType.entity')
    if (entity?.startsWith('basedata.Dimension')) {
      return type
    }
  })

  if (!components.length) return value
  const selectAndLeafComponents = components.filter(item => {
    if (DimensionData.filter(d => d.name == item.field).length > 0) {
      return item
    }
  })
  const needCheckValueKeys = []
  selectAndLeafComponents.forEach(item => {
    const { field } = item
    if (Object.keys(value).includes(field)) {
      needCheckValueKeys.push(field)
    }
  })

  if (!!needCheckValueKeys.length) {
    const params = []
    needCheckValueKeys.forEach(key => {
      const dimension = value[key]
      const id = getV(dimension, 'id') || dimension
      params.push({
        key,
        id
      })
    })
    const result = await api.invokeService(
      '@custom-dimension:check:Dimensions:isLeaf',
      params.map(item => item.id)
    )
    const notLeaf = result.filter(item => !item.isLeaf)
    if (notLeaf.length) {
      const ids = notLeaf.map(item => item.id)
      const needDelKey = params
        .filter(item => {
          if (item.id instanceof Array) {
            return item.id.filter(v => ids.includes(v)).length
          } else {
            return ids.includes(item.id)
          }
        })
        .map(item => item.key)

      needDelKey.forEach(delkey => {
        delete value[delkey]
      })
    }
  }
  return value
}

function formatDetailValue(item) {
  if (item.feeTypeForm.feeDate) {
    item.feeTypeForm.feeDate = new Date().getTime()
  }

  const feetypeForm = item.feeTypeForm
  const invoiceType = get(feetypeForm, 'invoiceForm.type')

  const { specificationId } = item
  const { components } = specificationId
  const dateFields = components.filter(el => el.type === 'date')
  dateFields.forEach(field => {
    const { defaultValue, withTime } = field
    if (defaultValue && defaultValue.value === 'submit.date') {
      item.feeTypeForm[field.field] = withTime
        ? moment(moment().format('YYYY/MM/DD HH:mm')).valueOf()
        : moment(moment().format('YYYY/MM/DD')).valueOf()
    }
  })

  //以下信息，不在复制单据时带入草稿
  if (invoiceType === 'exist') {
    //通过导入发票生成的明细
    const invoiceFormField = components.find(el => el.field === 'invoiceForm')
    const { editable, defaultValue } = invoiceFormField
    if (!editable) {
      item.feeTypeForm.invoiceForm = { type: defaultValue.value }
    } else {
      item.feeTypeForm.invoiceForm = { type: invoiceType }
    }

    const taxs = components.filter(line => get(line, 'defaultValue.type') === 'invoiceSum').map(line => line.field)
    taxs.forEach(key => {
      delete item.feeTypeForm[key]
    })
  }
  if (
    item.feeTypeForm.thirdPartyOrders || //通过导入中航易购生成的明细
    item.feeTypeForm.ordersData //通过导入易快报自营业务生成的明细（企业消费，公务卡等）
  ) {
    delete item.feeTypeForm.thirdPartyOrders
    delete item.feeTypeForm.ordersData
    delete item.feeTypeForm.orders
  }
  return item
}

function deleteKeys(value, specification, keys = []) {
  const { components } = specification
  components.forEach(component => {
    if (isNeedDel(component)) {
      keys.push(component.field)
    }
  })
  keys.push('code')
  keys.push('linkDetailEntities')
  keys.forEach(delkey => {
    delete value[delkey]
  })
  return value
}

function isNeedDel(component) {
  const { type, defaultValue, editable } = component
  const value = get(defaultValue, 'value', '')
  const isOfficialCardSettlement = defaultValue?.type === 'officialCardSettlement'
  return (
    type === 'dataLink' ||
    type === 'dataLinkEdits' ||
    (value === 'submit.requisition' && !editable) ||
    isOfficialCardSettlement
  )
}

export function canChangeTemplateFn(dataSource) {
  return getNodeValueByPath(dataSource, 'plan.submitNode.nextId', 'SUBMIT') === 'SUBMIT'
}

//提交单据时 对补充单据的外理逻辑
export function submitBillLogic({ formValue, CHANGJIEPay, bus, isAdmin, multiplePayeesMode, needCertificateNo = [] }) {
  const { payeeId = {}, submitterId = {}, details = [], payPlanMode, payPlan = [] } = formValue
  const staffId = get(payeeId, 'staffId', {}) || {} //staffId 可能为null
  const canEdit = isAdmin || staffId.id === submitterId.id
  // 如果是多收款人模式
  if (CHANGJIEPay && multiplePayeesMode) {
    const payeeList = api.getState('@common.payees') || []
    let payeeIdMap = {}
    let waitForComplement = details.filter(
      item =>
        item.feeTypeForm.feeDetailPayeeId &&
        (!item.feeTypeForm.feeDetailPayeeId.certificateType || !item.feeTypeForm.feeDetailPayeeId.certificateNo) &&
        item.feeTypeForm.feeDetailPayeeId.type === 'PERSONAL' &&
        needCertificateNo.includes(item.feeTypeForm.feeDetailPayeeId.id)
    )
    if (payPlanMode) {
      waitForComplement = payPlan.filter(item => {
        // @i18n-ignore
        const payee =
          payeeList.length &&
          payeeList.find(payee => payee.id === item.dataLinkForm[i18n.get('E_system_支付计划_收款信息')])
        // @i18n-ignore
        return (
          item.dataLinkForm['E_system_支付计划_收款信息'] &&
          payee &&
          (!payee.certificateType || !payee.certificateNo) &&
          payee.type === 'PERSONAL' &&
          needCertificateNo.includes(item.dataLinkForm['E_system_支付计划_收款信息'])
        )
      })
    }
    if (waitForComplement.length > 0) {
      if (payPlanMode) {
        waitForComplement.forEach(item => {
          // @i18n-ignore
          const payee =
            payeeList.length && payeeList.find(payee => payee.id === item.dataLinkForm['E_system_支付计划_收款信息'])
          // @i18n-ignore
          return (payeeIdMap[item.dataLinkForm['E_system_支付计划_收款信息']] = payee)
        })
      } else {
        waitForComplement.forEach(
          item => (payeeIdMap[item.feeTypeForm.feeDetailPayeeId.id] = item.feeTypeForm.feeDetailPayeeId)
        )
      }
      const canEdit = isAdmin
      return api
        .open('@bills:CheckPayeeModal', {
          payeeIdList: Object.values(payeeIdMap),
          payeeIdMap,
          canEdit,
          multiplePayeesMode: multiplePayeesMode,
          billsBus: bus,
          formValue
        })
        .then(data => {
          if (data === 'cancel') {
            return data
          }
          if (canEdit) {
            let feeDetailPayeeId
            const { details, payPlanMode } = formValue
            if (!payPlanMode) {
              details.length > 0 &&
                details.forEach(item => {
                  feeDetailPayeeId = data[item.feeTypeForm.feeDetailPayeeId.id]
                  if (feeDetailPayeeId) {
                    item.feeTypeForm.feeDetailPayeeId = feeDetailPayeeId
                  }
                })
            }
            if (bus) {
              bus.setFieldsValue({ details: [...details] })
            }
            return formValue
          } else {
            return Promise.reject()
          }
        })
    }
  } else if (
    payeeId &&
    CHANGJIEPay &&
    (!payeeId.certificateType || !payeeId.certificateNo) &&
    payeeId.type === 'PERSONAL'
  ) {
    // 单收款人模式
    return bus.invoke('get:pay:money').then(payMoney => {
      return api.open('@bills:CheckPayeeModal', { payeeId, canEdit }).then(data => {
        if (data === 'cancel') {
          return data
        }
        if (canEdit) {
          formValue.payeeId = data
          if (bus) {
            bus.setFieldsValue({ payeeId: data })
          }
          return formValue
        } else {
          return Promise.reject()
        }
      })
    })
  }
  return formValue
}

//这4种状态下 提交单据时需要给multiplePayeesMode赋值
const billsUnsavedState = {
  new: true,
  modify: true,
  draft: true,
  rejected: true
}

//审批中修改单据和复制单据时设置值
export function setFormMultiplePayeesMode(formValue, dataSource = {}) {
  const { payeeId = {} } = formValue
  if (!formValue.multiplePayeesMode && billsUnsavedState[dataSource.state] && payeeId?.multiplePayeesMode) {
    formValue.multiplePayeesMode = payeeId.multiplePayeesMode
    formValue.payPlanMode = payeeId.payPlanMode
    formValue.payeePayPlan = payeeId.payeePayPlan
  }
}

//form表单上面后台返回的一些数据需要在单据保存的时候重新塞回去
export function updateFormData(dataSource, formValue) {
  if (dataSource && formValue) {
    const outerCode = get(dataSource, 'form.outerCode')
    if (outerCode) {
      formValue.outerCode = outerCode
    }
  }
}

function hasApprotion(value) {
  const newValue = cloneDeep(value)
  const details = getV(newValue, 'details', [])
  let hasApportion = false
  details.forEach(item => {
    const apportions = getV(item, 'feeTypeForm.apportions', [])
    if (!!apportions.length) {
      hasApportion = true
      delete item.feeTypeForm.apportions
    }
  })
  return { hasApportion, newValue }
}

export async function checkValue(params) {
  const { oldTemplate, newTemplate, bus, dataSource, remunerationBatchField } = params
  const { data, value } = await parseValueApprotion({ newTemplate, bus, dataSource, remunerationBatchField })
  let nV = await checkTempKeepCurrencyEqual({ oldTemplate, newTemplate, value })
  nV = await isReceivingAmountEquel({ oldTemplate, newTemplate, value })
  return { data, value: nV }
}

export async function checkTempKeepCurrencyEqual({ oldTemplate, newTemplate, value }) {
  return new Promise(async (resolve, reject) => {
    const OTMustAllFeeTypeCurrencyEqual = getMustAllFeeTypeCurrencyEqual(oldTemplate)
    const NTMustAllFeeTypeCurrencyEqual = getMustAllFeeTypeCurrencyEqual(newTemplate)
    if (OTMustAllFeeTypeCurrencyEqual === NTMustAllFeeTypeCurrencyEqual) return resolve(value)
    const details = getV(value, 'details', [])
    if (!details.length || isCurrencyEquel(details)) return resolve(value)
    if (NTMustAllFeeTypeCurrencyEqual) {
      showModal.confirm({
        title: i18n.get('即将移除所有费用明细'),
        content: i18n.get(
          '新模板要求所有费用币种一致。当前单据存在不同币种的费用。切换到新的单据模板将会导致所有费用被移除。'
        ),
        onOk: () => {
          delete value.details
          resolve(value)
        },
        onCancel: () => {
          reject()
        },
        okText: i18n.get('移除'),
        cancelText: i18n.get('取消')
      })
    } else {
      resolve(value)
    }
  })
}

async function isReceivingAmountEquel({ oldTemplate, newTemplate, value }) {
  let isChange = false
  const { allowSelectionReceivingCurrency: oldAllow, currencyRange: oldCurrencyRange } = getPayConfig(oldTemplate)
  const { allowSelectionReceivingCurrency: newAllow, currencyRange: newCurrencyRange } = getPayConfig(newTemplate)
  const receivingCurrency = getBillReceivingCurrency(value)
  const currencyEquel = arraysStringAreEqual(oldCurrencyRange, newCurrencyRange)

  return new Promise(async (resolve, reject) => {
    // 没有收款币种的话清除receivingAmount属性
    if ((newAllow && !currencyEquel && value?.details?.length) || (oldAllow && !newAllow && value?.details?.length)) {
      isChange = true // 范围变更
    }
    if (!newAllow && receivingCurrency) {
      delete value.receivingCurrency
      delete value.payeeId?.receivingCurrency
    }

    if (isChange) {
      showModal.confirm({
        title: i18n.get('提示'),
        content: i18n.get('新模板的收款币种配置不一致，切换到新的单据模板收款金额会清空，更新后请检查费用明细。'),
        onOk: () => {
          value.details = clearDetailsReceivingAmount(value.details)
          resolve(value)
        },
        onCancel: () => {
          reject()
        },
        okText: i18n.get('我知道了'),
        cancelText: i18n.get('取消')
      })
    } else {
      resolve(value)
    }
  })
}

function arraysStringAreEqual(arr1, arr2) {
  if (isArray(arr1) && isArray(arr2)) {
    if (arr1.length !== arr2.length) return false
    return arr1.every(item => arr2.includes(item))
  }
  return false
}

function isCurrencyEquel(details) {
  let result = true
  let beforeCode = ''
  for (let i = 0; i < details.length; i++) {
    const detail = details[i]
    const foreignNumCode = getV(detail, 'feeTypeForm.amount.foreignNumCode', '')
    if (i !== 0) {
      result = foreignNumCode === beforeCode
    }
    if (!result) {
      break
    }
    beforeCode = foreignNumCode
  }
  return result
}

function getMustAllFeeTypeCurrencyEqual(template) {
  const components = getV(template, 'components', [])
  const detailComponent = components.find(c => c.field === 'details')
  return getV(detailComponent, 'mustAllFeeTypeCurrencyEqual', false)
}

export function parseValueApprotion(params) {
  const { newTemplate, bus, dataSource, remunerationBatchField } = params
  return new Promise(async (resolve, reject) => {
    if (!newTemplate || !bus) reject()
    const value = await bus.getValue()
    const remunerationBatch = get(dataSource, `form.${remunerationBatchField}`)
    if (remunerationBatch) {
      value[remunerationBatchField] = remunerationBatch
    }

    resolve({ data: newTemplate, value })
  })
}

export function getValidateErrorByShow(components, errKeys = []) {
  return components.filter(v => !v.hide && errKeys.includes(v.field))
}

export function showSensitiveContent(dataSource, userId) {
  const owner = get(dataSource, 'ownerId')
  const submitter = get(dataSource, 'form.submitterId')

  const ownerId = typeof owner === 'object' ? owner.id : owner
  if (ownerId === userId) return true

  const submitterId = typeof submitter === 'object' ? submitter.id : submitter
  if (submitterId === userId) return true

  const nodes = get(dataSource, 'plan.nodes') || []
  let show = false
  for (let node of nodes) {
    const { counterSigners, approverId } = node
    if (approverId && approverId.id === userId) {
      show = true
      break
    }
    if (counterSigners && counterSigners.length > 0) {
      const signer = counterSigners.find(item => item.signerId.id === userId)
      if (signer) {
        show = true
        break
      }
    }
  }
  return show
}

export const STATE_LIST = ['paid', 'archived', 'rejected'] //这些状态下不显示敏感信息

// 校验借款包是否存在，是否可以被单据关联
export function checkLoanPackage(writtenOff, submitterId) {
  let loanIds = writtenOff.map(item => {
    // 兼容报销单关联多条申请单
    return typeof item.loanInfoId === 'object' ? item.loanInfoId.id : item.loanInfoId
  })
  return new Promise((resolve, reject) => {
    api.invokeService('@bills:check:loan:package:exist', loanIds, submitterId).then(res => {
      const { items } = res
      if (items && items.length) {
        let str = ''
        Object.keys(items[0]).forEach(a => {
          str += items[0][a] + i18n.get('；')
        })
        str = str.substring(0, str.length - 1)
        showMessage.error(str)
        reject(str)
      } else {
        resolve()
      }
    })
  })
}

export const errorStyle = {
  marginLeft: 2,
  width: 16,
  color: '#FF7C7C',
  height: 16
}

export function fnLinkDetailEntitiesValue(value) {
  const cValue = cloneDeep(value)
  const arrLinkDetails = get(cValue, 'linkDetailEntities', []) || []
  if (arrLinkDetails.length && arrLinkDetails[0].linkDetailEntityId) {
    // const linkDetailEntities = groupBy(arrLinkDetails, 'linkDetailEntityId.linkId')
    const linkDetailEntities = {}
    arrLinkDetails.forEach(item => {
      const key = item.linkDetailEntityId.linkRequisitionInfo || item.linkDetailEntityId.linkId
      let list = linkDetailEntities[key]
      if (!list?.length) {
        list = []
        linkDetailEntities[key] = list
      }
      list.push(item)
    })
    const list = []
    Object.keys(linkDetailEntities).forEach(key => {
      const dataList = linkDetailEntities[key].map(item => {
        const { amount, linkDetailEntityId, _tempConsumId } = item
        const { unwrittenOffAmount, id } = linkDetailEntityId
        const money = related.relatedMap[id]
        const modifyMoney = new MoneyMath(unwrittenOffAmount).add(amount).value
        const tempConsumId = _tempConsumId ? _tempConsumId : cValue.detailId
        linkDetailEntityId.unwrittenOffAmount = new MoneyMath(linkDetailEntityId.unwrittenOffAmount).add(money).value
        return { ...linkDetailEntityId, modifyMoney, modifyValue: amount, _tempConsumId: tempConsumId }
      })
      list.push({ flowId: key, dataList: dataList })
    })
    cValue.linkDetailEntities = list
  }
  return cValue
}

export async function checkTemplateField(oldTemplate, newTemplate, legalEntityCurrencyPower = false, bus) {
  if (legalEntityCurrencyPower) {
    const oldCurrecyField = oldTemplate?.components?.find(f => f.field === 'legalEntityMultiCurrency')
    const newCurrecyField = newTemplate?.components?.find(f => f.field === 'legalEntityMultiCurrency')
    if (oldCurrecyField && !newCurrecyField) {
      api.invokeService('@bills:update:dimention:currency', null)
      const currency = api.getState()['@common'].standardCurrency
      const rates = await api.dataLoader('@common.allCurrencyRates').load()
      bus.emit('dimention:currency:change', { rates, currency })
    }
  }
}

export function getFilterList() {
  const formTypes = [
    { label: i18n.get('全部模板'), type: 'all' },
    { label: i18n.get('报销单'), type: 'expense' },
    { label: i18n.get('借款单'), type: 'loan' },
    { label: i18n.get('申请单'), type: 'requisition' }
  ]
  if (api.getState()['@common'].powers?.BasicDocument) {
    //基础单据charge
    Array.prototype.push.apply(formTypes, [
      { label: i18n.get('付款单'), type: 'payment' },
      { label: i18n.get('通用审批单'), type: 'custom' }
    ])
  }
  if (api.getState()['@common'].powers?.RECEIPT_DOCUMENT) {
    //基础单据charge
    Array.prototype.push.apply(formTypes, [{ label: i18n.get('收款单'), type: 'receipt' }])
  }
  const stateChildren = [
    { label: i18n.get('全部状态'), type: 'all' },
    { label: i18n.get('待提交'), type: 'draft' },
    { label: i18n.get('审批中'), type: 'approving' },
    { label: i18n.get('已驳回'), type: 'rejected' },
    { label: i18n.get('待收单'), type: 'receiving' },
    { label: i18n.get('待寄送'), type: 'sending' },
    { label: i18n.get('待支付'), type: 'paying' }
    // { label: i18n.get('已完成(待确认)'), type: 'paid' },
    // { label: i18n.get('已完成(已确认)'), type: 'archived' }
  ]

  const entrustLists = [
    { label: i18n.get('全部委托'), type: 'ALL_DELEGATE' },
    { label: i18n.get('委托我的'), type: 'DELEGATE_TO_ME' },
    { label: i18n.get('我委托的'), type: 'MY_DELEGATE' }
  ]

  if (api.getState()['@common'].powers?.Express) {
    //收单异常
    stateChildren.push({ label: i18n.get('收单异常'), type: 'receivingExcep' })
  }

  const isHidingFinishedBills = enableHidingFinishedBills()
  if (isHidingFinishedBills) {
    stateChildren.push(
      {
        label: i18n.get('已完成'),
        type: 'paid',
      }
    )
  }

  return [
    {
      type: 'state',
      label: isHidingFinishedBills ? i18n.get('审批状态') : i18n.get('提交状态'),
      children: stateChildren
    },
    {
      type: 'formType',
      label: i18n.get('模板类型'),
      children: formTypes
    },
    {
      type: 'entrust',
      label: i18n.get('委托提单'),
      children: entrustLists
    }
  ]
}

export const formatDate = (value, format) => {
  if (!value) {
    return value
  }
  return moment(value).format(format)
}

// 费用类型的差异
export const getDiffsBetweenFeeTypes = (curDetails, prevDetails) => {
  const curFeeDetailMap = curDetails.reduce(
    (result, item) =>
      Object.assign(result, item?.feeTypeForm?.detailId ? { [item?.feeTypeForm?.detailId]: item } : undefined),
    {}
  )
  const prevFeeDetailMap = prevDetails.reduce(
    (result, item) =>
      Object.assign(result, item?.feeTypeForm?.detailId ? { [item?.feeTypeForm?.detailId]: item } : undefined),
    {}
  )
  const curRes = curDetails.map((item, index) => {
    const id = item?.feeTypeForm?.detailId
    const prev = prevFeeDetailMap[id]
    // 如果以前不存在，则是新增的
    if (!prev) {
      return {
        type: 'add',
        name: item?.feeTypeId?.fullname,
        time: item?.feeTypeId?.createTime,
        amount: item?.feeTypeForm?.amount,
        subText: [formatDate(item?.feeTypeForm?.feeDate, 'YYYY年MM月DD日')],
        text: []
      }
    } else {
      // 假如价格相同，则视为没有变更
      if (prev?.feeTypeForm?.amount.standard == item?.feeTypeForm?.amount.standard) {
        return null
      }
      return {
        type: 'change',
        name: item?.feeTypeId?.fullname,
        time: item?.feeTypeId?.createTime,
        amount: item?.feeTypeForm?.amount,
        subText: [formatDate(item?.feeTypeForm?.feeDate, 'YYYY年MM月DD日')],
        text: [],
        before: {
          name: prev?.feeTypeId?.fullname,
          time: prev?.feeTypeId?.createTime,
          amount: prev?.feeTypeForm?.amount,
          subText: [formatDate(prev?.feeTypeForm?.feeDate, 'YYYY年MM月DD日')],
          text: []
        }
      }
    }
  })

  const prevRes = prevDetails.map(item => {
    const id = item?.feeTypeForm?.detailId
    const cur = curFeeDetailMap[id]
    // 如果现在不存在，则是删除的
    if (!cur) {
      return {
        type: 'deled',
        name: item?.feeTypeId?.fullname,
        time: item?.feeTypeId?.createTime,
        amount: item?.feeTypeForm?.amount,
        subText: [formatDate(item?.feeTypeForm?.feeDate, 'YYYY年MM月DD日')],
        text: []
      }
    } else {
      return null
    }
  })
  return [...curRes, ...prevRes].filter(Boolean)
}

const formatInvoice = item => {
  const fileKey = item?.key
  if (fileKey) {
    return {
      name: item.fileName,
      time: null,
      thumbUrl: item.fileId?.thumbUrl || item.fileId?.url,
      url: item.fileId?.url || item.fileId?.thumbUrl
    }
  }
  const result = formatInvoiceInfoData(item)
  return {
    ...result,
    amountLabel: '税价合计：',
    subText: [result.date, result.code && '发票号码：' + result.code, '发票类型：' + result.typeName]
  }
}

/**
 * 转换发票数据
 * @param item
 * @returns {import('./types').InvoiceSimpleData}
 */
export const formatInvoiceInfoData = item => {
  const entityId = item?.master?.entityId
  const value = item?.master
  const mapItem = {
    [InvoiceEntityType.增值税发票]: {
      name: 'E_system_发票主体_销售方名称',
      date: 'E_system_发票主体_发票日期',
      code: 'E_system_发票主体_发票号码',
      type: 'E_system_发票主体_发票类别',
      amount: 'E_system_发票主体_价税合计'
    },
    [InvoiceEntityType.出租车票]: {
      name: `出租车发票(${value?.form?.E_system_出租车票_发票所在地})`,
      date: 'E_system_出租车票_上车时间',
      code: 'E_system_出租车票_发票号码',
      type: '出租车票',
      amount: 'E_system_出租车票_金额'
    },
    [InvoiceEntityType.过路费发票]: {
      name: `过路费发票(${value?.form?.E_system_过路费发票_入口} - ${value?.form?.E_system_过路费发票_出口})`,
      date: 'E_system_过路费发票_时间',
      code: 'E_system_过路费发票_发票号码',
      type: '过路费发票',
      amount: 'E_system_过路费发票_金额'
    },
    [InvoiceEntityType.定额发票]: {
      name: '定额发票',
      code: 'E_system_定额发票_号码',
      type: '定额发票',
      amount: 'E_system_定额发票_金额'
    },
    [InvoiceEntityType.铁路客票]: {
      name: `${value?.form?.E_system_火车票_上车车站}--${value?.form?.E_system_火车票_下车车站}`,
      code: 'E_system_火车票_号码', // 没有发票代码
      type: '铁路客票',
      date: 'E_system_火车票_乘车时间',
      amount: 'E_system_火车票_金额'
    },
    [InvoiceEntityType.航空运输电子客票行程单]: {
      name: `${value?.form?.E_system_航空运输电子客票行程单_出发站}--${value?.form?.E_system_航空运输电子客票行程单_到达站}`,
      code: 'E_system_航空运输电子客票行程单_电子客票号码', // 没有发票代码
      type: '机票行程单',
      date: 'E_system_航空运输电子客票行程单_乘机时间',
      amount: 'E_system_航空运输电子客票行程单_金额'
    },
    [InvoiceEntityType.客运汽车发票]: {
      name: `客运汽车发票（${value?.form?.E_system_客运汽车发票_出发车站}--${value?.form?.E_system_客运汽车发票_达到车站}）`,
      code: 'E_system_客运汽车发票_发票号码',
      type: '客运汽车票',
      date: 'E_system_客运汽车发票_时间',
      amount: 'E_system_客运汽车发票_金额'
    },
    [InvoiceEntityType.其他发票]: {
      name: '其他票据',
      date: 'E_system_其他_日期',
      type: '其他票据',
      amount: 'E_system_其他_金额'
    },
    [InvoiceEntityType.医疗发票]: {
      name: '医疗发票',
      date: 'E_system_医疗发票_开票日期',
      type: '医疗发票',
      code: 'E_system_医疗发票_票据号码',
      amount: 'E_system_医疗发票_金额合计'
    },
    [InvoiceEntityType.非税收入类票据]: {
      name: '非税收入类票据',
      date: 'E_system_非税收入类票据_开票日期',
      type: '非税收入类票据',
      code: 'E_system_非税收入类票据_票据号码',
      amount: 'E_system_非税收入类票据_金额合计'
    },
    [InvoiceEntityType.消费小票]: {
      name: value?.form?.E_system_消费小票_店名 || '无法识别店名',
      type: '消费小票',
      date: 'E_system_消费小票_时间',
      amount: 'E_system_消费小票_金额'
    },
    [InvoiceEntityType.机打发票]: {
      name: 'E_system_机打发票_销售方名称',
      code: 'E_system_机打发票_发票号码',
      type: '机打发票',
      date: 'E_system_机打发票_时间',
      amount: 'E_system_机打发票_金额'
    },
    [InvoiceEntityType.海外发票]: {
      type:value?.form?.E_system_海外发票_票据类型 || '海外发票',
    }
  }
  const oo = mapItem[entityId]
  const invoiceType = value?.form?.[oo.type] || oo.type
  let invoiceTypeText = INVOICE_TYPE()?.[invoiceType] ?? IDENTIFY_INVOICE_TYPE?.()?.[invoiceType] ?? invoiceType

  if (invoiceTypeText === 'E_system_发票主体_发票类别') {
    invoiceTypeText = i18n.get('增值税发票')
  }

  return {
    name: value?.form?.[oo.name] || oo.name,
    time: value?.createTime,
    entityId,
    type: invoiceType,
    typeName: invoiceTypeText,
    code: value?.form?.[oo.code],
    amount: value?.form?.[oo.amount],
    date: oo.date ? formatDate(value?.form?.[oo.date], 'YYYY年MM月DD日') : undefined
  }
}

export const getDiffsBetweenInvoices = (curDetails, prevDetails) => {
  let curInvoices = []
  let preInvoices = []
  curDetails.map(item => {
    const invoices = getV(item, 'feeTypeForm.invoiceForm.invoices', [])
    const attachments = getV(item, 'feeTypeForm.invoiceForm.attachments', [])
    curInvoices = curInvoices.concat(invoices).concat(attachments)
  })
  prevDetails.map(item => {
    const invoices = getV(item, 'feeTypeForm.invoiceForm.invoices', [])
    const attachments = getV(item, 'feeTypeForm.invoiceForm.attachments', [])
    preInvoices = preInvoices.concat(invoices).concat(attachments)
  })
  console.log(curInvoices, preInvoices)
  const curRes = curInvoices.map((item, index) => {
    const fileKey = item?.key // 发票照片
    // 发票id + 更新时间 作为唯一
    const id = item?.master?.id
    const updateTime = item?.master?.updateTime
    const entityId = item?.master?.entityId
    const prev = preInvoices.find(item => {
      if (fileKey) {
        return item?.key == fileKey
      } else {
        return item?.master?.id == id && item?.master?.updateTime == updateTime
      }
    })
    // 如果以前不存在，则是新增的
    if (!prev) {
      return {
        ...formatInvoice(item),
        type: 'add'
      }
    } else {
      // 假如价格相同，则视为没有变更
      if (prev?.master?.form?.['E_' + entityId + '_发票号码'] == item?.master?.form?.['E_' + entityId + '_发票号码']) {
        return null
      }
      return {
        ...formatInvoice(item),
        before: formatInvoice(prev),
        type: 'change'
      }
    }
  })
  const prevRes = preInvoices.map((item, index) => {
    const fileKey = item?.key // 发票照片
    // 发票id + 更新时间 作为唯一
    const id = item?.master?.id
    const updateTime = item?.master?.updateTime
    const entityId = item?.master?.entityId
    const cur = curInvoices.find(item => {
      if (fileKey) {
        return item?.key == fileKey
      } else {
        return item?.master?.id == id && item?.master?.updateTime == updateTime
      }
    })
    // 如果现在不存在，则是删除的
    if (!cur) {
      return {
        ...formatInvoice(item),
        type: 'deled'
      }
    } else {
      return null
    }
  })
  return [...curRes, ...prevRes].filter(Boolean)
}

export const getDiffsBetweenVersions = (type, curVersion, prevVersion) => {
  console.log(curVersion, prevVersion)
  const curDetails = curVersion.value.form.details.reverse()
  const prevDetails = prevVersion.value.form.details
  switch (type) {
    case 'documentType':
      return getDiffsBetweenFeeTypes(curDetails, prevDetails)
    case 'invoice':
      return getDiffsBetweenInvoices(curDetails, prevDetails)
  }
}

export function keepTwoDecimalFull(num, p) {
  let result = parseFloat(num)
  if (isNaN(result)) {
    return num
  }

  let multiplicator = Math.pow(10, p)

  result = Math.round(num * multiplicator) / multiplicator

  let s_x = result.toString()

  let pos_decimal = s_x.indexOf('.')

  if (pos_decimal < 0) {
    pos_decimal = s_x.length
    s_x += '.'
  }
  while (s_x.length <= pos_decimal + p) {
    s_x += '0'
  }
  return s_x
}

export async function getObjById(feeTypeForm, components) {
  const types = ['select', 'dataLink']
  const list = []
  const map = {}
  components?.length &&
    components?.forEach(item => {
      const type = item.type
      const value = feeTypeForm[item.field]
      if (types.includes(type) && value) {
        isArray(value)
          ? value.forEach(line => {
              list.push({ ref: item.field, id: isObject(line) ? line?.id : line })
            })
          : list.push({ ref: item.field, id: isObject(value) ? value?.id : value })
      }
    })
  if (!list.length) return feeTypeForm
  let result = await api.invokeService('@bills:get:metaby:ids', { value: list })
  const { items } = result?.items?.reduce(
    (result, item) => {
      if (!result.map[item.id]) {
        result.map[item.id] = item
        result.items.push(item)
      }
      return result
    },
    { items: [], map: {} }
  )

  items?.forEach(line => {
    const fields = list?.filter(item => item?.id === line.id)
    fields?.forEach(u => {
      if (map.hasOwnProperty(u.ref) && isArray(map[u.ref])) {
        map[u.ref].push(line)
      } else if (map.hasOwnProperty(u.ref) && typeof map[u.ref] == 'object') {
        map[u.ref] = [map[u.ref], line]
      } else {
        map[u.ref] = isArray(feeTypeForm[u.ref]) ? [line] : line
      }
    })
  })

  return map
}

export function splitTemplateToGroups(template = []) {
  if (!template.length) return { groupTemplate: template }
  const singleGroupField = { budgetAdjustDetails: 'budgetAdjustDetails', details: 'details', widget: 'widget' }
  const hasSingleGroupField = template.find(item => !!singleGroupField[item.name ?? item.type])
  const groupsCount = template.filter(item => item.type === 'group').length
  const groupsFirstPosition = template.findIndex(item => item.type === 'group')
  // 无分组组件或者有分组组件且分组组件在第一位且只有一个分组组件
  if ((groupsCount === 0 || (groupsFirstPosition === 0 && groupsCount === 1)) && !hasSingleGroupField) {
    return { groupTemplate: template, isGroup: false }
  }
  // 有多个分组情况
  const groupTemplate = []
  let groupPosition = 0
  template.forEach((item, index) => {
    if (singleGroupField[item.name ?? item.type]) {
      if (index !== 0) {
        const tempGroup = template.slice(groupPosition, index)
        const len = tempGroup.length
        // budgetAdjustDetails、details、widget 分组前的分割线不包含在内
        if (len > 0 && tempGroup[len - 1].type === 'separator') {
          groupTemplate.push(template.slice(groupPosition, index - 1))
        } else {
          groupTemplate.push(template.slice(groupPosition, index))
        }
      }
      groupTemplate.push(template.slice(index, index + 1))
      // budgetAdjustDetails、details、widget 分组后的分割线不包含在内
      if (template[index + 1]?.type === 'separator') {
        groupPosition = index + 2
      } else {
        groupPosition = index + 1
      }
    } else if (item.type === 'group' && index !== 0) {
      const group = template.slice(groupPosition, index)
      if (group.length > 0) {
        groupTemplate.push(template.slice(groupPosition, index))
        groupPosition = index
      }
    }
  })
  // 取最后的分组
  const lastGroup = template.slice(groupPosition, template.length)
  if (lastGroup.length) {
    groupTemplate.push(lastGroup)
  }
  return { groupTemplate, isGroup: true }
}

// 获取分摊预设规则
export const fnGetQualificationForm = (specification, feeTypeInfo, feetypeTemplate, isBatch = false) => {
  const apportionConfig = getV(specification, 'value.configs', []).find(el => el?.ability === 'apportion')
  const dataLinkFilter = getV(apportionConfig, 'apportionConfig.dataLinkFilter', [])

  const qualificationForm = []
  if (isBatch && dataLinkFilter.some(v => v?.right?.type === 'BILL_DETAIL_FIELD')) {
    let errorMsg = i18n.get('该分摊方式不支持批量分摊，请更换或在费用明细中添加分摊。')
    toast.error(errorMsg)
  } else {
    dataLinkFilter.forEach(el => {
      const type = el?.right?.type
      const value = el?.right?.value
      if (type === 'BILL_DETAIL_FIELD' && value && feetypeTemplate?.find(v => (v.name || v.field) === value)) {
        let val = ''
        if (feeTypeInfo?.[value]?.toString()) {
          let feeInfoValueType = Object.prototype.toString.call(feeTypeInfo?.[value])
          if (feeInfoValueType.includes('Array')) {
            let valueType = Object.prototype.toString.call(feeTypeInfo[value][0])
            if (valueType.includes('Object')) {
              val = feeTypeInfo[value].map(el => el?.id || '')
            } else {
              val = feeTypeInfo[value]
            }
          } else if (feeInfoValueType.includes('Object')) {
            if (feeTypeInfo[value]?.id) {
              val = feeTypeInfo[value]?.id || ''
            }
            if (feeTypeInfo[value]?.standard) {
              val = feeTypeInfo[value]?.standard || ''
            }
          } else {
            val = feeTypeInfo[value]
          }
        }
        qualificationForm.push({
          type: el?.right?.type,
          field: Array.isArray(value) ? value?.[0] || '' : value,
          value: val
        })
      }
    })
  }
  return qualificationForm
}

// 根据分摊预设规则进行分摊
export const fnApportionDetails = async (details, billSpecification) => {
  // 分摊预设规则需要提交人id
  const userinfo = api.getState()['@common'].userinfo
  const currentStaffId = getV(userinfo, 'staff.id')

  // 获取企业生效的分摊模板
  const apportionResult = await getAllApportionSpecification('apportion')
  const apportionSpecifications = apportionResult?.items || []

  const result = details.map(async detail => {
    const { feeTypeForm, specificationId, feeTypeId } = detail

    // 获取分摊模板
    const detailComponents = getV(detail, 'feeTypeId.expenseSpecification.components', [])
    const apportion = detailComponents.find(el => el?.type === 'apportions')

    // apportion.specificationIds中包含用户执行过删除操作的模板
    // 需要在企业的可用分摊模板中找到模板
    const apportionIds = getV(apportion, 'specificationIds', [])
    // apportionIds[0]是默认分摊模板的originalId
    const apportionSpecArr = []
    apportionSpecifications.forEach(el => {
      if (el.originalId === apportionIds[0]) {
        apportionSpecArr.unshift(el)
      } else if (apportionIds.includes(el.originalId)) {
        apportionSpecArr.push(el)
      }
    })
    const apportionSpec = apportionSpecArr[0]
    if (!apportionSpec) return detail

    // 获取分摊预设规则
    const apport = { value: apportionSpec }
    const qualificationForm = fnGetQualificationForm(apport, feeTypeForm, specificationId?.components)
    const params = {
      submitterId: currentStaffId,
      specificationId: billSpecification.id,
      feeTypeId: feeTypeId?.id,
      apportionName: specificationId?.name,
      apportionId: apport?.value?.id,
      qualificationForm
    }

    let res
    try {
      res = await api.invokeService('@bills:get:getShareListApi', params)
    } catch (err) {
      console.error(err)
    }
    if (!res) return detail
    let { apportions } = res?.value
    if (apportions?.length && feeTypeForm?.amount) {
      apportions = fnPercentageApportion(apportions, feeTypeForm?.amount)
      detail.feeTypeForm.apportions = apportions.map(el => {
        return {
          apportionForm: el,
          specificationId: apport.value
        }
      })
    }
    return detail
  })
  return await Promise.all(result)
}

// 根据分摊比例分摊
export const fnPercentageApportion = (data, amount) => {
  if (!data) return null
  const feeAmountBig = new Big(amount.standard)
  const FIXED_VALUE = amount.standardScale
  const length = data?.length
  let totalAmountNoLast = 0
  let smallThanZero
  let result = data.map((line, index) => {
    let percentBig = new Big(line.apportionPercent).div(new Big(100))
    let apportionMoneyStr = percentBig.times(feeAmountBig).toFixed(FIXED_VALUE) //本位币
    if (index === length - 1) {
      apportionMoneyStr = new Big(feeAmountBig).minus(totalAmountNoLast).toFixed(Number(FIXED_VALUE))
      if (apportionMoneyStr < 0) {
        smallThanZero = true
      }
    }
    totalAmountNoLast = new Big(totalAmountNoLast).plus(apportionMoneyStr).toFixed(Number(FIXED_VALUE))
    line.apportionMoney = standardCurrencyToForeign(
      apportionMoneyStr,
      line.apportionMoney || standardValueMoney('0.00')
    )
    return line
  })

  if (smallThanZero) {
    let totalAmountNoLast = 0
    result = data.map((line, index) => {
      let percentBig = new Big(line.apportionPercent).div(new Big(100))
      let apportionMoneyStr = percentBig
        .times(feeAmountBig)
        .round(FIXED_VALUE, 0)
        .toFixed(FIXED_VALUE)
      if (index === length - 1) {
        apportionMoneyStr = new Big(feeAmountBig).minus(totalAmountNoLast).toFixed(Number(FIXED_VALUE))
      }
      totalAmountNoLast = new Big(totalAmountNoLast).plus(apportionMoneyStr).toFixed(Number(FIXED_VALUE))
      line.apportionMoney = standardCurrencyToForeign(
        apportionMoneyStr,
        line.apportionMoney || standardValueMoney('0.00')
      )
      return line
    })
  }
  return result
}

/**
 * 判断单据是否使用了快速报销
 * @param dataSource
 * @returns {Promise<boolean>}
 */
export const checkQuickExpends = async dataSource => {
  // 快速报销直接发起单据过来的dataSource有插入的isQuickExpends，没有dataSource?.id
  let isQuickExpends = get(dataSource, 'requisitionInfo.isQuickExpends', false)
  // 草稿，驳回，提交过的单据有dataSource?.id，没有插入的isQuickExpends
  if (dataSource?.id) {
    // 查询单据明细是否是快速报销明细
    const res = await getDetailFLowRelation(dataSource?.id)
    isQuickExpends = res?.value === 'QUICK_EXPENSE'
  }
  return isQuickExpends
}

/**
 * 根据发票使用规则和单据风险判断提交送审时是否需要填写风险原因
 * @param id 单据id
 * @returns {Promise<T_RiskWarningContentItem[]>}
 */
export const getNeedSubmitRiskReasonList = async id => {
  const result = await Promise.all([
    api.invokeService('@invoice-manage:get:invoice:rule:list'),
    api.invokeService('@bills:get:flow:risk:warning', { id, reasonModify: true })
  ])
  const [invoiceRuleList, riskWarning] = result
  const needSubmitRiskReasonRuleList = invoiceRuleList?.items?.filter(
    item => item.control === 'ALLOW_SUBMIT_SHOW_RISK_EXPLAIN'
  )
  const needSubmitRiskReasonRuleMap = {}
  needSubmitRiskReasonRuleList.forEach(item => {
    needSubmitRiskReasonRuleMap[item.id] = item
  })
  const invoiceNormIdMap = {}
  const needSubmitRiskReasonList = riskWarning?.singleInvoiceRiskWarning?.reduce((result, curRiskWarning) => {
    const { invoiceId, invoiceMsg, relatedFlows, riskWarningReason = [] } = curRiskWarning
    const needSubmitRiskReasonListFilter = riskWarningReason.filter(
      reasonItem => !!needSubmitRiskReasonRuleMap[reasonItem.invoiceNormId]
    )
    needSubmitRiskReasonListFilter.forEach(item => {
      const { invoiceNormId, invoiceNormDesc, invoiceRiskExplainId, invoiceRiskExplainContent } = item
      if (invoiceNormIdMap[invoiceNormId] !== undefined) {
        result[invoiceNormIdMap[invoiceNormId]]?.riskWarningContent.push({
          invoiceNum: invoiceId,
          invoiceMsg,
          invoiceRiskExplainId,
          invoiceRiskExplainContent,
          relatedFlows: relatedFlows ? relatedFlows[invoiceNormDesc] : []
        })
      } else {
        invoiceNormIdMap[invoiceNormId] = result.length
        result.push({
          invoiceNormId,
          invoiceNormDesc,
          riskWarningContent: [
            {
              invoiceNum: invoiceId,
              invoiceMsg,
              invoiceRiskExplainId,
              invoiceRiskExplainContent,
              relatedFlows: relatedFlows ? relatedFlows[invoiceNormDesc] : []
            }
          ]
        })
      }
    })
    return result
  }, [])
  return needSubmitRiskReasonList
}

/**
 * 为 BillVersionDiffModal 展示发票风险组装数据
 * @params 通过 @bills:get:flow:risk:warning 获取的原始的风险数据
 * @returns 组装好的备份数据
 */
export const getRiskReasonDataForVersionDiffModal = riskData => {
  if (!riskData || !riskData.value) return riskData
  const riskDataClone = cloneDeep(riskData)
  const { singleInvoiceRiskWarning = [], value } = riskDataClone
  const riskReasonArr = []
  singleInvoiceRiskWarning?.forEach(riskWarningItem => {
    const { invoiceMsg, riskWarningReason } = riskWarningItem
    if (riskWarningReason?.length) {
      riskWarningReason.forEach(reasonItem => {
        const { invoiceNormDesc, invoiceRiskExplainContent } = reasonItem
        if (invoiceRiskExplainContent) {
          riskReasonArr.push({
            invoiceMsg,
            invoiceNormDesc,
            invoiceRiskExplainContent
          })
        }
      })
    }
  })
  value?.riskWarning?.forEach(riskWarningItem => {
    const { controlName, messages = [] } = riskWarningItem
    riskReasonArr
      .filter(item => `${item.invoiceNormDesc}：` === controlName)
      .forEach(item => {
        let index = messages.findIndex(message => item.invoiceMsg === message)
        if (index !== -1) {
          messages.splice(index + 1, 0, i18n.get('原因：{_k0}', { _k0: item.invoiceRiskExplainContent }))
        }
      })
  })
  return riskDataClone
}

/**
 * 单据信息支持分享到飞书个人对话框
 */
export const fnShareBillInfoFEISHU = async flowId => {
  const params = {
    flowId,
    sharingPlatform: 'FEI_SHU' // 分享平台 DT,APP,KdCloud,QYWX
  }
  const shareResult = await Fetch.POST('/api/flow/v1/sharing/getForwardingUrl', {}, { body: params })
  if (!shareResult?.value) {
    showMessage.success(i18n.get('链接失效'))
  }
  try {
    api?.sdk?.share({
      title: shareResult.value?.title || i18n.get('合思费控报销'),
      url: shareResult.value?.url,
      success: async res => {
        showMessage.success(i18n.get('已转发到会话中'))
      },
      fail: err => console.log(err)
    })
  } catch (error) {
    console.log(error)
    // alert(JSON.stringify(error))
  }
}

/**
 * 转发企业微信
 * @param {*} flowId
 */
export const fnShareBillInfoQW = async flowId => {
  const params = {
    flowId,
    sharingPlatform: 'QY_WX' //分享平台，默认钉钉，可以不传 DT,APP,KdCloud,QYWX
  }
  const shareResult = await Fetch.POST('/api/flow/v1/sharing/getForwardingUrl', {}, { body: params })
  if (!shareResult?.value) {
    showMessage.success(i18n.get('链接失效'))
  }
  try {
    api?.sdk?.onMenuShareAppMessage({
      title: shareResult.value?.title || i18n.get('合思费控报销'),
      link: shareResult.value?.url, // 分享链接；在微信上分享时，该链接的域名必须与企业某个应用的可信域名一致
      callback: res => {
        if (res.err_msg == 'shareWechatMessage:ok') {
          showMessage.success(i18n.get('已转发到会话中'))
        } else {
          alert(JSON.stringify(res))
        }
      }
    })
  } catch (error) {
    alert(JSON.stringify(error))
  }
}

export const fnShareBillInfo = flowId => {
  api?.sdk?.pickConversation({
    corpId: Fetch.corpId, // 钉钉中的企业id（ding******）
    isConfirm: false, // 选择会话时是否需要弹出确认弹窗
    onSuccess: async res => {
      const params = {
        flowId,
        sharingPlatform: 'DT', //分享平台，默认钉钉，可以不传 DT,APP,KdCloud,QYWX
        conversationId: res?.cid, //会话id
        deviceType: 'DESKTOP' // 设备类型： 默认DESKTOP-桌面端，MOBILE-移动端
      }
      const shareResult = await Fetch.POST('/api/flow/v1/sharing', {}, { body: params })
      if (shareResult?.value) {
        showMessage.success(i18n.get('已转发到钉钉会话'))
      }
    },
    onFail: err => console.log(err)
  })
}

/**
 * 单据详情页面添加分享按钮，仅支持钉钉平台
 * buttons: 详情页面中的操作按钮数组
 * flowId：单据中的flowId
 */
export const fnPushDingTalkShareBtn = (buttons, flowId) => {
  let shareBtn = null
  const params = qs.parse(location.search.slice(1))
  if (!!params?.sdkName?.length) {
    shareBtn = {
      label: i18n.get('转发'),
      onClick: () => {
        shareBillAction(flowId)
      }
    }
    buttons.push(shareBtn)
    return
  }

  if (window.__PLANTFORM__ === 'DING_TALK') {
    shareBtn = {
      label: i18n.get('转发'),
      onClick: () => shareBillAction(flowId)
    }
  } else if (window.__PLANTFORM__ === 'FEISHU') {
    shareBtn = {
      label: i18n.get('转发'),
      onClick: () => shareBillAction(flowId)
    }
  } else if (window.__PLANTFORM__ === 'WEIXIN') {
    shareBtn = {
      label: i18n.get('转发'),
      onClick: () => shareBillAction(flowId)
    }
  }

  shareBtn && buttons.push(shareBtn)
}

export const shareBillAction = flowId => {
  const params = qs.parse(location.search.slice(1))
  if (!!params?.sdkName?.length) {
    api?.sdk?.share({
      flowId
    })
    return
  }

  if (window.__PLANTFORM__ === 'DING_TALK') {
    fnShareBillInfo(flowId)
  } else if (window.__PLANTFORM__ === 'FEISHU') {
    fnShareBillInfoFEISHU(flowId)
  } else if (window.__PLANTFORM__ === 'WEIXIN') {
    fnShareBillInfoQW(flowId)
  }
}

export function checkCSCMoney(feeTypeForm, item, sumUseBalance) {
  const standard = get(feeTypeForm, `${item.field}.standard`, '')
  const standardScale = get(feeTypeForm, `${item.field}.standardScale`)
  const standardNumber = Number(standard)
  const sumUseBalanceNumber = Number(Number(sumUseBalance)?.toFixed(standardScale))
  // 公务卡金额范围：
  // 手动计算字段：正数：0.01<= 金额 <=公务卡订单可用金额  负数：公务卡订单可用金额<= 金额 <=-0.01, 0: 公务卡订单可用金额总和为0（正加负）
  // 自动计算字段：金额 ===公务卡订单可用金额
  if (item?.editable) {
    const zeroRangeErr = standardNumber === 0 && sumUseBalanceNumber !== 0
    const positiveRangeErr = standardNumber > 0 && !(standardNumber >= 0.01 && standardNumber <= sumUseBalanceNumber)
    const negativeRangeErr = standardNumber < 0 && !(standardNumber <= -0.01 && standardNumber >= sumUseBalanceNumber)
    return !standard || zeroRangeErr || positiveRangeErr || negativeRangeErr
  } else {
    return !standard || standardNumber !== sumUseBalanceNumber
  }
}

export function filterCSCFields(components) {
  const fields = components.filter(item => {
    const defaultValueType = get(item, 'defaultValue.type')
    if (
      defaultValueType === 'officialCardMoney' ||
      defaultValueType === 'officialCardSettlement' ||
      item.field === 'amount'
    ) {
      return item
    }
  })
  return fields
}
/*
  单据连号风险提示显示更多逻辑, 需求详情：https://hose2019.feishu.cn/wiki/wikcnSVzL9thRdF1GS8QhZ9yKIf
  web端很少这种交互方式方式，但是产品坚持，只能勉强实现，如果有更好的处理建议，请联系我，谢谢
*/
export const showMoreBtnOnWarningOrErrorTips = parentDom => {
  const flowQueryDomArr = (parentDom || document).querySelectorAll('.flow-query')
  if (flowQueryDomArr?.length) {
    Array.prototype.forEach.call(flowQueryDomArr, flowQueryDom => {
      const flowQueryDomWidth = flowQueryDom?.offsetWidth
      const flowQueryParentDomWidth = flowQueryDom?.parentElement?.offsetWidth
      if (flowQueryDomWidth && flowQueryParentDomWidth && Math.abs(flowQueryDomWidth - flowQueryParentDomWidth) < 60) {
        const detailMoreDom = flowQueryDom?.parentElement?.querySelector('.detail-more')
        if (detailMoreDom) {
          detailMoreDom.style.display = 'unset'
        }
      }
    })
  }
}

/**
 * 获取要打开的模板切换窗口类型
 */
export const getChangeTemplateModalKey = () => {
  const universal = api.getState()['@common'].powers.Universal
  if (universal) {
    return '@bills:ChangeStandardTemplateModal'
  }
  return '@bills:NewChangeTemplateModal'
}

/**
 * 随手记日志
 * @param data
 * @param billSpecificationId
 */
export const logCreateBillFromRecordNote = ({ data = [], billSpecificationId }) => {
  try {
    const userInfo = api.getState()['@common'].userinfo?.data
    const corpId = userInfo?.staff?.corporationId?.id
      ? userInfo?.staff?.corporationId.id
      : userInfo?.staff?.corporationId
    const logObj = {
      corpId,
      staffId: userInfo?.staff?.id,
      logTime: Date.now(),
      billSpecificationId,
      recordNoteIds: data
    }
    createBillFromRecordNote(logObj)
  } catch (error) {
    console.log(error)
  }
}

/**
 * 根据originalId获取模板
 */
const getSpecifications = params => {
  return specifications.GET('/[ids]', params)
}

/**
 * 根据originalId检查模板是否可用
 * @param specificationOriginalId
 * @returns {boolean}
 */
export const checkSpecificationActive = async specificationOriginalId => {
  const originalId = specificationOriginalId?.id || specificationOriginalId
  const res = await getSpecifications({ ids: [originalId] })
  const specActive = get(res, 'items[0].active', true)
  if (!specActive) {
    Modal.warning({
      okText: i18n.get('知道了'),
      title: i18n.get('复制失败'),
      content: i18n.get('该单据所用的单据模板已停用，请启用后再试')
    })
    return false
  }
  return true
}

export const billSelectStaff = async (data, multiple = false, types = ['department-member']) => {
  // types默认不传了就默认选中内部员工
  let checkedList = types?.length
    ? types.map(type => ({
        type,
        checkIds: data.checkedKeys ?? []
      }))
    : [
      {
        type: 'department-member',
        checkIds: data.checkedKeys ?? []
      }
    ]
  if (data?.checkedList?.length) {
    checkedList = data.checkedList.map(item => ({ type: item.type, checkIds: item.checkedKeys }))
  }
  let staffLimitData = undefined
  // 可选范围指定的人员
  if (data.dataSource?.length) {
    staffLimitData = data.dataSource.map(item => item.id)
  } else if (data.isByRule) {
    // 通过规则限制可选人员范围
    const ruleStaffs = await getRuleStaff()
    staffLimitData = ruleStaffs.map(item => item.id)
  }
  const params = {
    multiple,
    required: data.required,
    followContactRules: data.followContactRules,
    staffLimitData,
    fetchDataSourceAction: {
      staff: api.invokeService('@organizationManagement:get:visibility:staffs')
    },
    notFollowExternalChargeRules: data?.allowExternalStaff
  }
  const checkedData = await api.open('@organizationManagement:SelectStaff', { data: checkedList, ...params })
  // 为了兼容老的选人弹框选中数据的结构做了一下处理
  if (data?.checkedList?.length) {
    const result = checkedData.map(item => {
      return {
        ...item,
        checkedData: item.checkList
      }
    })
    return { checkedList: result }
  } else {
    return checkedData?.reduce((result, item) => result.concat(item?.checkList || []), [])
  }
}
export const getRuleStaff = async () => {
  const result = await api.dataLoader('@common.staffRangeByRule').load()
  return result.data ?? []
}

export const fnFilterSpecification = (specificationGroups = []) => {
  const autoExpenseForbiddenFeaturePower = api.getState('@common.powers').autoExpenseForbiddenFeature
  if (!autoExpenseForbiddenFeaturePower) {
    return specificationGroups
  }
  return specificationGroups.filter(specification => {
    specification.specifications = specification.specifications.filter(specification => {
      return !(specification.type === 'expense' || specification.type === 'loan')
    })
    return !!specification.specifications.length
  })
}
// 获取模版上对当前人员隐藏的字段
export const getSpecificationHiddenFields = async specification => {
  if (!specification?.id) {
    return []
  }
  try {
    const result = await getSpecificationHiddenFieldsById(specification.id)
    return result?.items ?? []
  } catch (e) {
    return []
  }
}

export const getBatchSpecificationHiddenFields = async ids => {
  if (!ids.length) {
    return {}
  }
  try {
    const data = await batchGetSpecificationHiddenFieldsByIds(ids)
    const result = {}
    data?.items.forEach(item => {
      result[item.specificationId] = item.fieldsHideResult
    })
    return result
  } catch (e) {
    return {}
  }
}

/**
 * 根据费用明细的值获取分摊风险path对应的分摊id和费用明细id
 */
export const getApportionIdMap = details => {
  if (!details?.length) return {}
  const map = {}
  details.forEach(detail => {
    const detailId = detail?.feeTypeForm?.detailId
    const apportions = detail?.feeTypeForm?.apportions
    if (!apportions?.length || !detailId) return
    apportions.forEach(apportion => {
      const apportionId = apportion?.apportionForm?.apportionId
      if (!apportionId) return
      map[`${detailId}$${apportionId}`] = { detailId, apportionId }
    })
  })
  return map
}

// 是否允许加签和转交
export const canShiftNodeApproveFlow = (staff, currentNode, submitNode) => {
  if (!staff?.external) {
    // 内部员工默认可以加签
    return true
  }
  if (submitNode?.externalStaffConfig?.shiftLaunchApprover) {
    return isIncludesCurrentNodeId(submitNode, currentNode)
  }
  return false
}

// 是否允许加签
export const canAddNodeApproveFlow = (staff, currentNode, submitNode) => {
  if (!staff?.external) {
    // 内部员工默认可以加签
    return true
  }
  if (submitNode?.externalStaffConfig?.shiftLaunchApprover) {
    return isIncludesCurrentNodeId(submitNode, currentNode)
  }
  return false
}

// 是否允许下载附件
export const canDownAttachmentFLow = (staff, currentNode, submitNode) => {
  if (!staff?.external) {
    // 内部员工默认可以
    return true
  }
  return submitNode?.externalStaffConfig?.downAttachmentApprover
}

// 是否允许打印单据
export const canPrintFlow = (staff, currentNode, submitNode) => {
  if (!staff.external) {
    // 内部员工默认可以
    return true
  }
  if (submitNode?.onlyOwnerPrint && submitNode?.ownerId === staff.id) {
    if (submitNode?.ownerAndApproverPrintNode?.length) {
      return submitNode.ownerAndApproverPrintNode.includes(currentNode.configNodeId)
    }
    return true
  }
  return submitNode?.externalStaffConfig?.printFlowApprover
}

const isIncludesCurrentNodeId = (submitNode, currentNode) => {
  if (!submitNode?.externalStaffConfig?.shiftPlanNodeId || !submitNode?.externalStaffConfig?.shiftPlanNodeId?.length) {
    // 如果shiftPlanNodeId为空时默认是true
    return true
  }
  return submitNode.externalStaffConfig.shiftPlanNodeId.includes(currentNode.configNodeId)
}

// 获取单据模版名称：英文还是中文
export const rememberBillMoreExpandStatus = (userInfo, isGet, value, key = 'open') => {
  if (isGet) {
    const session = JSON.parse(window.localStorage.getItem(`__userOpenRightBillMoreInfo${userInfo?.staff?.id}`) || '')
    return session?.open // === undefined ? false : session?.open
  } else {
    try {
      let data =
        JSON.parse(window.localStorage.getItem(`__userOpenRightBillMoreInfo${userInfo?.staff?.id}`) || '') || {}
      data = {
        ...data,
        [key]: value
      }
      window.localStorage.setItem(`__userOpenRightBillMoreInfo${userInfo?.staff?.id}`, JSON.stringify(data))
    } catch (e) {
      console.log(e)
    }
  }
}

export const rememberFlowPlanExpandStatus = (userInfo, isGet, value) => {
  if (isGet) {
    const data = JSON.parse(window.localStorage.getItem(`__userOpenRightBillMoreInfo${userInfo?.staff?.id}`) || '')
    return data?.flowPlanOpen
  } else {
    try {
      let data =
        JSON.parse(window.localStorage.getItem(`__userOpenRightBillMoreInfo${userInfo?.staff?.id}`) || '') || {}
      data = {
        ...data,
        flowPlanOpen: value
      }
      window.localStorage.setItem(`__userOpenRightBillMoreInfo${userInfo?.staff?.id}`, JSON.stringify(data))
    } catch (e) {
      console.log(e)
    }
  }
}
// 判断是否是被 hab 绑定字段
export const isHabBinding = (billSpecification = {}, field) => {
  const { components } = billSpecification
  if (!components || !field) return false
  const result = components
    .filter(com => com.type === 'widget')
    .find(item => {
      if (item.assignmentRule && item.assignmentRule.fields) {
        return item.assignmentRule.fields.some(i => i.targetField === field)
      }
      return false
    })
  return !!result
}

export const detailsTemplateAddReceivingAmount = details => {
  return details.map(d => {
    const { specificationId, feeTypeForm } = d
    if (feeTypeForm.receivingAmount) {
      const template = getBillTemplateAddFeeReceivingAmount(specificationId.components, true)
      specificationId.components = template
    }
    return d
  })
}

export const fnFormatMoneyValue = ({ data, specification }) => {
  if (!data || !specification) {
    return data
  }
  fnCheckComponentMoneyValue(specification, data.form || data)
  const details = data?.details || data?.form?.details || []
  if (details.length) {
    details.forEach(item => {
      const { feeTypeForm, specificationId } = item
      if (feeTypeForm && specificationId) {
        fnCheckComponentMoneyValue(specificationId, feeTypeForm)
      }
    })
  }
  return data
}

const fnCheckComponentMoneyValue = (specification, form) => {
  if (!specification || !form) {
    return
  }
  const { components } = specification
  const moneyFields = components.filter(c => c.type === 'money')
  if (moneyFields.length) {
    moneyFields.forEach(component => {
      const { field } = component
      const value = form[field]
      if (value) {
        form[field] = fnCheckMoneyValue(value)
      }
    })
  }
}

export const fnCheckMoneyValue = moneyValue => {
  if (!moneyValue) {
    return moneyValue
  }
  const { foreign, foreignNumCode, sysRate, foreignStrCode } = moneyValue
  if (foreignNumCode === undefined && foreign === undefined) {
    delete moneyValue.rate
    delete moneyValue.foreignScale
    delete moneyValue.foreignStrCode
    delete moneyValue.foreignSymbol
    delete moneyValue.foreignUnit
    delete moneyValue.sysRate
  }
  if (getBoolVariation('cyxq-78648') && !foreignNumCode && !foreignStrCode) {
    delete moneyValue.foreignScale
    delete moneyValue.foreignStrCode
    delete moneyValue.foreignSymbol
    delete moneyValue.foreignUnit
    delete moneyValue.sysRate
    delete moneyValue.foreignNumCode
    delete moneyValue.foreign
  }
  if (foreignNumCode !== undefined && sysRate === undefined) {
    moneyValue.sysRate = moneyValue.rate
  }
  return moneyValue
}

// 生成唯一的会话ID
export const generateAIChatSessionId = () => {
  return `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}
