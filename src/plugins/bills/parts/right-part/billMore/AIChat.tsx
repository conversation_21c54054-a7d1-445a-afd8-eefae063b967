import React, { useEffect, useState, useRef, useCallback } from 'react'
import { app } from '@ekuaibao/whispered'
import { isEqual } from 'lodash'
import Postmate from 'postmate'
import { handleApplyAIResult, removeAiMarkForField, formatDataForCreateBills } from './flowTools'
import styles from './AIChat.module.less'
import { Modal, message, SkeletonParagraph, SkeletonNormalList } from '@hose/eui'
import { removeEmptyObjects } from '../../../../../components/dynamic/parseAIResult'
import { generateAIChatSessionId } from '../../../util/billUtils'
import { saveFlow } from '../../../bills.action'
import { Fetch } from '@ekuaibao/fetch'
import { AI_CHAT_CREATE_BILLS_KEY, AichatCreateBillCache } from '../../../../../lib/constants'



const {
  clientHost = "https://ai-chat.ekuaibao.net",
  serverHost = "https://air.ekuaibao.net",
  idpDomain = 'https://idp.ekuaibao.net'
} = window.ENV_CONFIG || {}

const iframeClientHost = process.env.NODE_ENV === 'development' ? "http://localhost:5566" : clientHost

const getSpecificationIdIgnoreVersion = (specificationId) => {
  if (!specificationId) return ''
  const version = specificationId.lastIndexOf(':')
  return version > 0 ? specificationId.slice(0, version) : specificationId
}

// 自定义 Hook: 聊天初始化
const useChatInitialization = (billDetails: any) => {
  const [sessionId, setSessionId] = useState(() => window.CURRENT_AICHAT_SESSIONID || generateAIChatSessionId())
  const [specificationId, setSpecificationId] = useState<string>('');
  const [flowId, setFlowId] = useState<string>('')
  const [flowCode, setFlowCode] = useState<string>('')

  useEffect(() => {
    const specificationId = billDetails?.form?.specificationId || billDetails?.currentSpecification
    const id = billDetails?.id
    const code = billDetails?.form?.code
    if (specificationId) {
      const id = typeof specificationId === 'string'
        ? specificationId
        : specificationId?.id
      setSpecificationId(id || '')
    }
    setFlowCode(code || '')
    setFlowId(id || '')
  }, [billDetails?.form?.specificationId, billDetails?.currentSpecification])

  return { sessionId, setSessionId, specificationId, flowCode, flowId }
}

const AIChat = (props: any) => {
  const { isActiveInfo, bus, templates, billDetails = {}, chatId = '' } = props
  const [code, setCode] = useState('')
  const [aiFilledFields, setAiFilledFields] = useState({})
  const [error, setError] = useState<string | null>(null)

  const childRef = useRef<any>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const latestPropsRef = useRef({ aiFilledFields, setAiFilledFields, bus, templates })

  // 使用聊天初始化 Hook
  const { sessionId, setSessionId, specificationId, flowId, flowCode } = useChatInitialization(billDetails)

  // 更新最新的 props 引用
  useEffect(() => {
    latestPropsRef.current = { aiFilledFields, setAiFilledFields, bus, templates }
  }, [aiFilledFields, setAiFilledFields, bus, templates])


  const onCreateBills = async (
    result,
    noNeedConfirm?: boolean,
    specifications?: any[],
  ) => {
    const viewType = localStorage.getItem(`BillViewType-${app.getState()['@common'].userinfo?.staff?.id}`) || 'list'
    async function createBills(result, specifications) {
      let datas: any = Array.isArray(result) ? result : [result]
      if (!specifications) {
        specifications = (await app.invokeService('@bills:get:Specifications', datas.map(item => item.specificationId), true))?.items
      }
      datas = await formatDataForCreateBills(datas, specifications)
      let successLines = []
      // 清洗数据
      for (let key in datas) {
        const item = datas[key]
        const { aiFilledFields, ...rest } = item
        const num = datas?.length > 1 ? `第${Number(key) + 1}个` : ''
        message.loading(i18n.get(`${num}单据创建中...`), 0)
        const action = saveFlow(rest, {
          actionType: 'save', hiddenMsg: true, aiSave: true
        })

        let current: any = {}
        try {
          current = await action?.payload
          current.flow && (current.id = current.flow.id)
          if (current.id) {
            const aichatCreateBills: AichatCreateBillCache[] = localStorage.getItem(AI_CHAT_CREATE_BILLS_KEY) ? JSON.parse(localStorage.getItem(AI_CHAT_CREATE_BILLS_KEY)) : []
            aichatCreateBills.push({
              id: current.id,
              aiFilledFields
            })
            localStorage.setItem(AI_CHAT_CREATE_BILLS_KEY, JSON.stringify(aichatCreateBills))
            message.destroy()
            message.success(i18n.get(`成功创建${num}单据，请查看`))
            successLines.push(current)
          }
        } catch (error) {
          message.destroy()
          message.error(i18n.get(`${num}单据创建失败：${error.msg || error.errorMessage}`))
        }
      }

      if (successLines.length > 0) {
        viewType === 'list' && app.emit('update:bill:list', null)
        if (viewType === 'table' && typeof bus?.reload === 'function') {
          bus.reload()
        }
      }

      return successLines
    }

    function handleSuccessLines(successLine) {
      viewType === 'list' && bus.has('list:select:change') && bus.emit('list:select:change', successLine)
      viewType === 'table' && bus.has('table:row:click') && bus.emit('table:row:click', successLine?.flow)
    }

    if (noNeedConfirm) {
      const successLines = await createBills(result, specifications)
      if (successLines.length > 0) {
        handleSuccessLines(successLines[0])
      }
      return
    }

    Modal.confirm({
      title: i18n.get('是否保存当前单据，并打开新单据？'),
      content: i18n.get('当前单据未保存，是否要保存到草稿'),
      okText: i18n.get('保存并退出'),
      middleText: i18n.get('不保存'),
      cancelText: i18n.get('仍停留本页面'),
      onOk: async () => {
        // 保存当前单据，自动定位到创建好的最后一个
        const successLines = await createBills(result, specifications)
        if (successLines.length > 0) {
          bus.getValueWithValidate(1)
            .then(async (formValue) => {
              const res = await bus.invoke('save:bill:click', formValue, {
                updateSelectLine: false,
              })
              if (res && res?.state === "success") {
                setTimeout(() => {
                  handleSuccessLines(successLines[0])
                  Modal.destroyAll();
                }, 0)
              }
            })
        }
      },
      onMiddle: async () => {
        // 放弃当前单据，自动定位到创建好的最后一个
        const successLines = await createBills(result, specifications)
        if (successLines.length > 0) {
          handleSuccessLines(successLines[0])
          Modal.destroyAll();
        }
      },
      onCancel: async () => {
        // 只创建
        createBills(result, specifications)
      }
    })

  }

  // 表单值变化处理函数
  const handleFormValueChange = useCallback((changedValues) => {
    if (!changedValues) return

    Object.keys(changedValues).forEach(fieldName => {
      const aiInfo = aiFilledFields[fieldName]
      if (Array.isArray(changedValues[fieldName])) {
        const justIds = changedValues[fieldName].map(item => item.id).filter(Boolean)
        // 人员多选会再变更一次对象
        if (justIds.length > 0 && isEqual(justIds, aiInfo)) {
          return
        }
      }

      // 人员单选也会变更一次对象
      if (typeof changedValues[fieldName] === 'object') {
        const justId = changedValues[fieldName]?.id
        if (justId && isEqual(justId, aiInfo)) {
          return
        }
      }

      // 提交人处理, 对象和 id 对比一致，也不要再执行清理动作
      if (fieldName === 'submitterId' && aiInfo && changedValues[fieldName]?.id === aiInfo) {
        return
      }

      // 这个情况就是附件了
      const isAttachments = Array.isArray(aiInfo) &&  aiInfo.every(item => item?.fileName && item?.fileId && item?.key)
      if(isAttachments) {
        if(isEqual(changedValues[fieldName]?.map(item => item?.key), aiInfo?.map(item => item?.key))) return 
      }

      if (aiInfo && !isEqual(removeEmptyObjects(changedValues[fieldName]), removeEmptyObjects(aiInfo))) {
        removeAiMarkForField(fieldName, billDetails?.feeDetailId)
      }
    })
  }, [aiFilledFields, billDetails?.feeDetailId])

  useEffect(() => {
    bus && bus.on('bill:value:changed:forAIAttachment', handleFormValueChange)
    return () => {
      bus && bus.un('bill:value:changed:forAIAttachment', handleFormValueChange)
    }
  }, [handleFormValueChange, bus])

  // 获取认证代码
  useEffect(() => {
    app.invokeService('@auth-check:getIdpTokenExchangeCode', {
      clientId: 'hose-ai-chat',
      redirectUri: clientHost
    }).then((res: any) => {
      setCode(res?.access_token)
    }).catch((error: any) => {
      console.error('获取认证代码失败:', error)
      setError('认证失败')
    })
  }, [])

  function initSpecifications() {
    let specificationGroupsList = app.getState('@custom-specification').specificationGroupsList
    let specifications = specificationGroupsList.reduce((pre, next) => {
      return pre.concat(next.specifications)
    }, [])
    return specifications
  }

  // 初始化 Postmate 连接
  useEffect(() => {
    if (!code || !containerRef.current || !sessionId) return

    const initPostmate = async () => {
      try {
        // 清理旧的连接
        if (childRef.current) {
          childRef.current.destroy()
          childRef.current = null
        }

        const params: Record<string, string> = {
          serverHost,
          idpHost: idpDomain,
          code,
          sessionId,
          specificationId,
          flowId,
          flowCode,
          chatId,
          corpId: Fetch.ekbCorpId,
          auditFlowCharge: app.getState()['@common'].powers.ApprovalCopilot ? '1' : '0'
        }

        const paramsStr = new URLSearchParams(params).toString()
        const handshake = new Postmate({
          container: containerRef.current!,
          url: `${iframeClientHost}?${paramsStr}`,
          classListArray: [styles['ai-chat-iframe']],
        })

        const child = await handshake
        childRef.current = child

        // 监听子页面事件
        child.on(`${chatId}ai:fillFormResult`, async (data: any) => {
          const { aiFilledFields, setAiFilledFields, bus, templates } = latestPropsRef.current
          if (Array.isArray(data.data)) {
            const specifications = await app.invokeService('@bills:get:Specifications', data.data.map(item => item.specificationId), true)
            Modal.confirm({
              title: i18n.get('是否创建新的单据？'),
              content: <div className={styles.createFlowModalContent}>
                <div>{i18n.get('当前未打开需要AI填单的单据，无法直接进行填单。点击创建后，将为你填写以下单据：')}</div>
                <ul>
                  {specifications.items.map(item => {
                    return <li key={item.id}>{item.name}</li>
                  })}
                </ul>
              </div>,
              okText: i18n.get('创建'),
              cancelText: i18n.get('取消'),
              onOk: () => {
                onCreateBills(data?.data, !specificationId, specifications.items)
              }
            })
            return
          }
          if (!specificationId) {
            Modal.confirm({
              title: i18n.get('是否创建新的单据？'),
              content: i18n.get('当前未打开需要AI填单的单据，无法直接进行填单，点击创建单据后将为你填单。'),
              okText: i18n.get('创建'),
              cancelText: i18n.get('取消'),
              onOk: () => {
                onCreateBills(data?.data, true)
              }
            })
            return
          }
          if (getSpecificationIdIgnoreVersion(specificationId) !== getSpecificationIdIgnoreVersion(data?.data?.specificationId)) {
            Modal.confirm({
              title: i18n.get('是否创建新的单据？'),
              content: i18n.get('当前单据模板不匹配，无法直接进行填单，点击创建单据后将为你填单。'),
              okText: i18n.get('创建'),
              cancelText: i18n.get('取消'),
              onOk: () => {
                onCreateBills(data?.data, false)
              }
            })
            return
          }
          handleApplyAIResult({
            aiFilledFields,
            setAiFilledFields,
            result: data?.data,
            bus,
            components: templates,
          })
        })

        child.on(`${chatId}startNewChat`, () => {
          const newSessionId = generateAIChatSessionId()
          window.CURRENT_AICHAT_SESSIONID = newSessionId
          setSessionId(newSessionId)
        })

        child.on(`${chatId}ai:previewAttachmentOrInvoice`, (data) => {
          app.open('@bills:InvoicesOrAttachmentsModal', data?.data);
        })

        child.on(`${chatId}previewFile`, (data) => {
          console.log(data?.data, 'look')
          const line = data?.data
          if (line) {
            if (line.mimeType.includes('image/')) {
              app.emit('@vendor:preview:images', [line?.url], line?.url)
            } else if (line.mimeType.includes('pdf')) {
              app.emit('@vendor:preview:pdf', line?.url)
            } else {
              app.emit('@vendor:preview', line?.url, line?.name)
            }
          }
        })

        let specifications = initSpecifications()
        if (specifications?.length === 0) {
          await app.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
          specifications = initSpecifications()
        }
        const feeTypesData = await app.dataLoader('@common.feetypes').load()
        const postData = {
          isActiveInfo,
          sessionId,
          specifications,
          feeTypesData,
          formComponents: templates
        }
        console.log('准备发送配置数据:', postData)
        await childRef.current.call('updateConfig', postData)

        console.log('配置数据发送完成')
        // postmate 连接成功就意味着 ready
        setError(null)

      } catch (error) {
        console.error('Postmate 初始化失败:', error)
        setError('连接失败')
      }
    }

    initPostmate()

    return () => {
      if (childRef.current) {
        childRef.current.destroy()
        childRef.current = null
      }
    }
  }, [code, sessionId, specificationId])


  // 如果没有认证代码，显示加载状态
  if (!code) {
    return (
      <div className={styles['ai-chat-loading']}>
        <div className={styles['ai-chat-loading-right']}>
          <SkeletonParagraph animated />
        </div>
        <div className={styles['ai-chat-loading-left']}>
          <SkeletonNormalList animated showTitle showText showAvatar showAction />
        </div>
      </div>
    )
  }


  return (
    <div ref={containerRef} className={styles['ai-chat-container']}>
    </div>
  )
}

export default AIChat