/**************************************************
 * Created by nany<PERSON><PERSON>feng on 11/07/2017 14:17.
 **************************************************/
import styles from './RightPartHeader.module.less'
import React, { PureComponent } from 'react'
import LayoutBtnGroups from '../../../../../elements/layoutBtnGroups'
import CreditPointLabel from './CreditPointLabel'
import { Tooltip, Popover, Tag, Button } from '@hose/eui'
import { FilledGeneralBuzz, OutlinedGeneralState, OutlinedGeneralLayout, OutlinedTipsInfo } from '@hose/eui-icons'
import { app } from '@ekuaibao/whispered'
import { getLayoutConfig } from '../../../bills.action'
import { getDisplayName, getStaffShowByConfig } from '../../../../../elements/utilFn'
import { getSpecificationName } from '../../../util/billUtils'
import { billStateMap } from '../../../elements/newBillItem/staticUtil'
import TextCollapsed from './TextCollapsed'
import clx from 'classnames'

const isWx = window.__PLANTFORM__ === 'WEIXIN'

export default class RightPartHeader extends PureComponent {
  state = {
    config: null,
    isFullscreen: false
  }
  componentDidMount() {
    this.getConfig(this.props?.dataSource?.form?.specificationId?.id)
  }
  getConfig = async specificationId => {
    if (!specificationId) {
      return null
    }
    const powersCodeMap = app.getState('@common').powers.powerCodeMap
    // 开通charge 后就设置 webShow
    if (powersCodeMap.includes('170043')) {
      const res = await getLayoutConfig(specificationId)
      const webShow = res?.value?.configDetail?.webShow
      this.setState(
        {
          config: webShow
        },
        () => {
          this.setDataSource('170043_webShow', webShow)
        }
      )
    } else {
      this.removeDataSource('170043_webShow')
    }
  }

  setDataSource = (key, obj = {}) => {
    if (isWx) {
      session.set(key, JSON.stringify(obj))
    } else {
      localStorage.setItem(key, JSON.stringify(obj))
    }
  }

  removeDataSource = key => {
    if (isWx) {
      session.remove(key)
    } else {
      localStorage.removeItem(key)
    }
  }
  close = () => {
    this.props.bus.invoke('check:value:changed').then(
      res => {
        this.props?.layer?.emitCancel()
      },
      error => {
        if (error === 'cancel') return
        this.props?.layer?.emitCancel()
      }
    )
  }

  handleChangeModalSize = () => {
    const { isFullscreen } = this.state
    const domArray = document.getElementsByClassName('ant-drawer-content-wrapper')
    const dom = domArray.length > 0 ? domArray[domArray.length - 1] : domArray[0]
    if (!dom) {
      return
    }
    if (isFullscreen) {
      dom.className = dom.className.replace(/ fullScreenDrawer/g, '')
    } else {
      dom.className = dom.className + ' fullScreenDrawer'
    }
    // 为配合实现单据多列的响应式布局，触发mbox上的windowSizeChange方法
    window.onresize(undefined)
    this.setState({ isFullscreen: !isFullscreen })
  }

  render() {
    const { config } = this.state
    let {
      dataSource = {},
      isInHistory,
      offsetWidth,
      showWidget,
      className,
    } = this.props
    let { form = {}, state, ownerId = { name: '' }, logs = [], id } = dataSource
    let {
      title = '',
      specificationId = {},
      code = '',
      submitterId = {},
      systemGeneration = false,
      subsidyGeneration
    } = form
    const name = getSpecificationName(specificationId)
    let ownerName = getStaffShowByConfig(submitterId)
    if (submitterId.id !== ownerId.id) {
      const subName = getDisplayName(submitterId)
      const ownName = getDisplayName(ownerId)
      ownerName = i18n.get('submitted-by', {
        name: subName,
        ownerName: ownName
      })
    }

    logs = logs || []

    let submitNodes = logs.filter(v => v.action === 'freeflow.submit').map(v => v.attributes)

    let newUrgentConfig = submitNodes[submitNodes.length - 1] || {}
    let { isUrgent, urgentReason } = newUrgentConfig
    const { tagColor, label } = billStateMap()[state] || {}
    const isReadOnlyState = state !== 'modify' && state !== 'rejected' && state !== 'draft'
    const isShowLayoutBtnGrup = isInHistory || isReadOnlyState
    const alterFlag = form.alterFlag >= '1'

    const MAX_LENGTH_OF_TITLE = 14

    return (
      <div className={clx(styles.right_part, className)}>
        <div className="header">
          <div className="center">
            <div className="title">
              <Tooltip title={title}>
                <span
                  data-testid="bill-title"
                  className='translate-ignore-class'
                  data-title={title}
                >
                  <TextCollapsed id='bill-title' text={title} />
                </span>
              </Tooltip>
              {isUrgent && (
                <Tooltip title={urgentReason} placement='bottom'>
                  <Tag color="danger" fill="invert" className="ml-12 title-tag">
                    {i18n.get('加急')}
                    <OutlinedTipsInfo fontSize={12} />
                  </Tag>
                </Tooltip>
              )}
              {!isInHistory && label && (
                <Tag color={tagColor} className="ml-8 title-tag" fill="outline">
                  {label}
                </Tag>
              )}
              {(subsidyGeneration === 'surplus' || !!systemGeneration) && (
                <Tag color="default" className="ml-8 title-tag">
                  {i18n.get('自动创建')}
                </Tag>
              )}
              {alterFlag && (
                <Tag color="default" className="ml-8 title-tag">
                  {i18n.get('变更{__k0}次', { __k0: form.alterFlag })}
                </Tag>
              )}
            </div>
          </div>
          <div className="right fs-16">
            {showWidget && (
              <Button category='text' onClick={e => this.props.bus.emit('bills:view:diff', e)}>
                <OutlinedGeneralState fontSize={14} />
                <span className="text">{i18n.get('辅助')}</span>
              </Button>
            )}
            <Popover
              arrowPointAtCenter
              placement="bottomRight"
              content={<LayoutBtnGroups offsetWidth={offsetWidth} defaultConfig={config} />}
              trigger="click"
            >
              {!(config && config.forbidStaffModify) && isShowLayoutBtnGrup && (
                <Button category='text'>
                  <OutlinedGeneralLayout fontSize={14} />
                  <span className="text">{i18n.get('布局')}</span>
                </Button>
              )}
            </Popover>
          </div>
        </div>
        <div className="sub-title">
          <div className="list">{code}</div>
          <div className="list translate-ignore-class">{name}</div>
          <div className="list translate-ignore-class">{ownerName}</div>
          <div className="list status">
            <CreditPointLabel flowId={id} submitterId={submitterId?.id} ownerId={ownerId?.id} />
          </div>
        </div>

      </div>
    )
  }
}
