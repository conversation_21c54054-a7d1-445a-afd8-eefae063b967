import React, { useRef, useState } from 'react'
import LocalStore from '../../../../../lib/local-store'

interface TextCollapsedProps {
  /**
   * 缓存唯一标识
   */
  id: string
  text: string
  maxCount?: number
}

const getStorageKeyById = (id: string) => `textCollapsed:${id}`
const MAX_LENGTH_OF_TITLE = 30

export default function TextCollapsed({ id, text, maxCount = MAX_LENGTH_OF_TITLE }: TextCollapsedProps) {
  const storeRef = useRef(new LocalStore(getStorageKeyById(id)))
  const [isCollapsed, setIsCollapsed] = useState(storeRef.current.get())


  const handleClick = () => {
    setIsCollapsed(!isCollapsed)
    storeRef.current.set(!isCollapsed)
  }

  if (text.length <= MAX_LENGTH_OF_TITLE) {
    return <span>{text}</span>
  }

  return (
    <span>
      {text.slice(0, isCollapsed ? maxCount : text.length)}
      {isCollapsed ? '...' : ''}
      <span onClick={handleClick} style={{ font: 'var(--eui-font-body-r1)', color: 'var(--eui-primary-pri-500)', marginLeft: 12 }}>{isCollapsed ? i18n.get('展开') : i18n.get('收起')}</span>
    </span>
  )
}
