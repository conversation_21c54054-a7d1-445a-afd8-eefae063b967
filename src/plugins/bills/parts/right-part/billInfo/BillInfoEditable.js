/**************************************************
 * Created by nanyuantingfeng on 11/07/2017 14:51.
 **************************************************/
// 单据详情的内容
import React, { PureComponent } from 'react'
import { Button, message } from '@hose/eui'
import classnames from 'classnames'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import DynamicEditable from '../../../views/DynamicEditable'
import MessageCenter from '@ekuaibao/messagecenter'
import RightBottomLine from '../../../elements/RightBottomLine'
import { showMessage, showModal } from '@ekuaibao/show-util'
import exitConfirm from '../../../util/exitConfirm'
import WarningOrErrorTips from '../../../elements/WarningOrErrorTips'
import WarningSensitive from '../../../elements/WarningSensitive'
import UpdateTemplate from '../../../elements/UpdateTemplate'
import LogsCardView from '../../../../../elements/ekbc-business/bills/LogsCardView'
import {
  calculateFlowNodes,
  canChangeTemplateFn,
  checkLoanPackage,
  checkTemplateField,
  checkValue,
  formatNewTemplateValue,
  getDefSpecificationDS,
  getValidateErrorByShow,
  showSensitiveContent,
  getObjById,
  checkQuickExpends,
  STATE_LIST,
  getNeedSubmitRiskReasonList,
  checkCSCMoney,
  filterCSCFields,
  splitTemplateToGroups,
  formatExpenseLinkInfo,
  billSelectStaff,
  getSpecificationHiddenFields,
  getApportionIdMap,
  getSpecificationName,
  updateFormData,
  sortFeeTypeForm
} from '../../../util/billUtils'
import { cloneDeep, countBy, debounce, forEach, get, isEqual, uniq, once } from 'lodash'
import moment from 'moment'
import {
  checkIsRemuneration,
  fixRemunerationSpecification,
  getTripEditable,
  formatPayPlanData
} from '../../../../../lib/lib-util'
import {
  handleDimentionCurrencyChange,
  handlerCurrencyMoneySelectChange,
  setDefaultCurrencyMoney
} from '../../../util/defaultCurrency.js'

import {
  canModifyApproveMoney,
  fnGetDetailsParams,
  fnParseFormValue,
  getPayConfig,
  needUpdateCustomizeResult,
  needUpdateResult,
  parseApproveModifyLimitField,
  parseAsFormTemplate,
  parseAsFormValue,
  parseAsMeta,
  parseAsReadOnlyFormTemplate,
  parseFlowRisk,
  parseFormValueAsParam,
  parseSubmitParamAsFormValue,
  presetFormulaValue,
  fnGetDetailsParamsForChangeTemplate,
  parseFlowRiskV2
} from '../../../util/parse'
import {
  checkSubmitValue,
  formatValue,
  getParam,
  setDataLinkFormValue,
  updateAutoCalResult,
  checkAttrChange,
  formatAttrValue
} from '../../../util/autoCalculate'
import {
  callCustomizeCalByQueue,
  checkCustomizeSubmitValue,
  updateCustomizeCalResult
} from '../../../util/customizeCalculate'
import WrittenOffPart from '../../../elements/writtenoff/WrittenOffPart'
import {
  calPlanInstance,
  getApplyByExpense,
  getApplyByExpenseWithOrder,
  getAutoCalculationResult,
  getCalculateRecordLink,
  getAutoGenerationFeeDetail,
  getAutoGenerationFeeDetailRules,
  getCalculateField,
  isShowEbotInfo,
  saveFlow,
  saveMultiplePayeed,
  setValidateError,
  searchExtensionCenterConfig,
  searchMyBillList,
  recordInvoiceRiskWarningFetchOnly,
  setDetailFLowRelation,
  getOfficialCardSetting,
  upgradeOrderMicro,
  getFlowTravelResult,
  getEffectiveCurrencyInfo
} from '../../../bills.action'
import { inputInvoiceImport } from './BillImport/invoiceImport'
import {
  delDetailsExternalsForBillInfoEditable,
  getExtraRiskWarningList,
  getRiskFieldNum,
  getRiskInTemplate,
  getValidRiskWarningData,
  resetFieldsExternalsForBillInfoEditable,
  fnIsRiskError
} from '../../../riskWarning/formatRiskWarningData'
import '../../../../../file/iconfont'
import decorators from '@ekuaibao/lib/lib/decorators'
import styles from './BillInfoEditable.module.less'
import { MoneyMath } from '@ekuaibao/money-math'
import PayPlanWrapper from '../../../../../elements/payPlan/PayPlayTableWrapper'
import PayPlanStore from '../../../../../elements/payPlan/table/table.store'
import { Provider } from 'mobx-react'
import { toJS } from 'mobx'
import { isAllowModifyFiled } from '../../../../../components/utils/fnDisableComponent'
import { getDetailCalculateMoney, total, showLoading, hideLoading } from '@ekuaibao/lib/lib/lib-util'
import { fnCheckCompleted, fnInvoiceValid } from '../../../../../components/validator/validator'
import { getV, isNegat } from '@ekuaibao/lib/lib/help'
import getDefaultValue from '../../../../../components/utils/getInitialValue'
import { getFeeTypeById, getAllowSelectionReceivingCurrency } from '../../../../../lib/fee-util'
import { related } from '../../../../../elements/feeDetailViewList/Related'
import { uuid, isObject } from '@ekuaibao/helpers'
import { getMoney } from '../../../../../lib/misc'
import { checkBudgetOccupy } from '../../../util/checkBillBudgetOccupy'
import { callFnByQueue } from '../../../util/callFnByQueue'
import { callFnByQueueNew } from '../../../util/callFbByQueue'
import { AutoCalculate2 } from '../../../util/autoCalculate2'
import { Fetch } from '@ekuaibao/fetch'
import {
  filterDataLinkFields,
  getDataLinkPermissionFieldComponent,
  getDependenceFieldOfDataLink,
  handleAutoAssignOfOneResultOfDataLink
} from '../../../layers/dataLink/dataLinkUtil'
import AutoRepaymentPart from '../../../elements/autoRepayment/AutoRepaymentPart'
import AutoRepaymentPartReadOnly from '../../../elements/autoRepayment/AutoRepaymentPart.readonly'
import { enableDraftConfig } from './validateTripSync'
import { trackFn } from '../../../util/trackBill'
import { summarySelectType } from '../../../../../components/consts'
import { getTrueKey } from '../../../../../lib/utils'
import { constantValue } from '../../../../../components/utils/fnInitalValue'
import { fnParseTemplateFields, fetchAttachmentConfig } from '../../../../../lib/fnParseTemplateField'
const getSpecificationIconByName = api.require('@elements/specificationIcon')
import {
  leaveFlowPerformanceStatistics,
  endOpenFlowPerformanceStatistics,
  reportBillPagePaintDuration,
  startSaveFlowPerformanceStatistics,
  endFlowFormDataSubmitStatistics,
  endFlowFormDataCollectionStatistics,
  startOpenFlowThirdPartyPerformanceStatistics,
  flowDetailsStatistics
} from '../../../../../lib/flowPerformanceStatistics'
import { getBoolVariation, enableNewBillOptimization, enableFlowOptimization, newVersionOPaymenAccount, enableOtherInvoiceByDimension, supportBillDetailsSwitchingInDrawer } from '../../../../../lib/featbit'
import { logEvent } from '../../../../../lib/logs'
import { useNewAutomaticAssignment } from '../../../../../components/utils/fnAutoDependence'
import { getFlowPlanFromLogs, fetchNodesAIAgentMap } from '../../../../../elements/ai-agent-utils'
import { AIFillFormTracker } from '../../../../../lib/aiFillFormTracker'
import { polyfillForAIRiskData } from '../../../riskWarning/ai-audit-result/utils'
import { handlePayInfoOfSpecifationChange } from './BillInfoEditable.utils'

@EnhanceConnect(
  state => {
    return {
      writtenOffSummaryForMutiCurrency: state['@bills'].writtenOffSummaryForMutiCurrency,
      remunerationBatchField: state['@remuneration'].remunerationBatchField,
      baseDataProperties: state['@common'].globalFields.data,
      specificationGroupsList: state['@custom-specification'].specificationGroupsListByTYpe,
      originSpecificationGroupsList: state['@custom-specification'].specificationGroupsList,
      userInfo: state['@common'].userinfo.data,
      requisitionInfo: state['@bills'].requisitionInfo,
      currentVisibleFeeTypes: state['@bills'].currentVisibleFeeTypes,
      fieldMap: state['@common'].fieldMap,
      lastChoice: state['@common'].lastChoice.choiceValue,
      multiplePayeesMode: state['@bills'].multiplePayeesMode,
      payPlanMode: state['@bills'].payPlanMode,
      payeePayPlan: state['@bills'].payeePayPlan,
      baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap,
      CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
      isFilterStaffAndDept: state['@common'].organizationConfig.allowPermissionsEffect,
      validateError: state['@bills'].validateError,
      travelBlackList: state['@bills'].travelBlackList,
      dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo,
      legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
      KA_NUMBER_FIELD_COUNT_RULE: state['@common'].powers.KA_NUMBER_FIELD_COUNT_RULE,
      //补助自动计算 2.0
      KA_NUMBER_FIELD_COUNT_RULE_2: state['@common'].powers?.powerCodeMap?.includes?.('170023') ?? false,
      canUseDefaultCurrency: state['@common']?.powers?.isSetDefaultCurrency, //默认币种设置
      customizeQueryPower: state['@common'].powers.customizeQuery,
      AutoGenerateFeeDetail: state['@common'].powers.AUTO_GENERATE_FEE_DETAIL, //自动生成费用明细
      ExpressCharge: state['@common'].powers.Express, // 异地单据寄收管理
      civilServiceCard: state['@common'].powers.CivilServiceCard,
      externalStaffPower: state['@common'].powers.ExternalAuth,
      newBillTableList: state['@bills'].newBillTableList,
      standardCurrency: state['@common'].standardCurrency,
      writtenOffSummaryForCrossCurrency: state['@bills'].writtenOffSummaryForCrossCurrency,
      billsRateList: state['@bills'].billsRateList,
      allCurrencyRates: state['@common'].allCurrencyRates
    }
  },
  {
    saveFlow,
    calPlanInstance,
    getApplyByExpense,
    getApplyByExpenseWithOrder,
    getAutoCalculationResult,
    getCalculateRecordLink,
    saveMultiplePayeed,
    getCalculateField,
    setValidateError
  }
)
export default class BillInfoEditable extends PureComponent {
  attachmentConfigPromise = null
  timeFieldPromise = null
  hiddenFieldsPromise = null

  checkPromise = undefined
  constructor(props) {
    super(props)
    const { dataSource, riskData, isModify, bus } = props
    const systemGeneration = props?.dataSource?.systemGeneration
    const subsidyGeneration = props?.dataSource?.subsidyGeneration
    startOpenFlowThirdPartyPerformanceStatistics()
    bus.has('bills:check:nulllify:rule') && bus.invoke('bills:check:nulllify:rule')
    this.valuation = {}
    this.visibleSpecificationList = []
    // 关联申请中的明细
    this.expenseLinkDetails = []
    this.writtenOffFromEventBus = []
    this.isInitAutoAssignOfOneResultOfDataLink = true //是否是第一次赋值，第一次是取的所有的自动赋值的字段，后面再根据字段变更单独赋值，否则会漏掉一部分字段
    this.copyDetials = get(dataSource, 'form.details', [])
    const flowId = get(dataSource, 'id')
    const state = get(dataSource, 'state')
    const flowIdForFeeTypeModify = get(props, 'feeTypeVisibleObjForModify.flowId')
    let feeTypeVisibleObjForModify
    let showAllFeeType = true
    if (isModify) {
      showAllFeeType = get(props, 'feeTypeVisibleObjForModify.showAllFeeType')
      if (flowId && flowId !== flowIdForFeeTypeModify) showAllFeeType = true
      feeTypeVisibleObjForModify = showAllFeeType ? undefined : props.feeTypeVisibleObjForModify
    }
    this.isNeedInitCallDetailChangeFunc = false
    this.initCallDetailChangeFuncValue = []
    this.loanMoney = Number(dataSource?.flowValue?.loanMoney?.standard ?? 0)
    this.countRuleCheckFields = props.KA_NUMBER_FIELD_COUNT_RULE
      ? (state === 'new'
        ? get(
          dataSource,
          'currentSpecification.components', // 正常新建报销单据
          get(dataSource, 'requisitionInfo.defaultSpecification.components', []) // 通过申请单创建报销单据
        )
        : get(dataSource, 'form.specificationId.components', [])
      ) // 草稿编辑单据
        .filter(item => item.countRuleCheck)
        .map(item => ({
          fieldKey: item.field,
          countRule: item.countRule
        }))
      : []
    this.submitControl = {
      originalId: null,
      receivingExceptData: [],
      hasConfig: false,
      isSubmit: true,
      tipsMessage: ''
    }
    this.state = {
      template: [],
      hiddenFields: [], // 模版上需要隐藏的字段
      currentSpecification: undefined,
      value: {},
      writtenOffRecords: [],
      requisitionForm: {},
      submitterId: {}, //管理委托人，所有委托人信息请从this.state.submitterId取值
      formRiskWarningData: {}, //超标提醒数据
      extraRiskWarningList: void 0,
      riskFieldNum: 0,
      riskInTemplateList: [],
      riskInfo: {},
      flowId: '',
      errorMsg: '',
      showAllFeeType,
      feeTypeVisibleObjForModify,
      applicationListDetails: [],
      riskData,
      generationRule: [],
      referenceDataType: {},
      isAutoRepayment: false,
      repaymentRecords: [],
      dependenceFieldOfDataLink: [], // 业务对象字段依赖字段集
      isQuickExpends: false,
      isEnableDraftConfig: false,
      travelTemplateList: [], // 行程卡模板列表
      systemGeneration, // 结余补助相关信息
      subsidyGeneration, // 结余补助相关信息
      nodesAIAgentMap: {}
    }
    decorators?.layoutMenuWillLeave('myBill')(this.handleCheckValueChange.bind(this))
    decorators?.layoutMenuWillLeave('homepage')(this.handleCheckValueChange.bind(this))
    this.__CURRENT_DYNAMIC_VALUE = parseAsFormValue(dataSource, true)
    this.__CURRENT_IS_CHANGED = false
    this.editedDetailList = []
    //用来记录最新的 details 数据, 在 修改了 writtenOff 后拿来用
    this.newestDetails = this.copyDetials || []
    if (!this.PayPlanStore && dataSource.formType === 'expense') {
      this.PayPlanStore = new PayPlanStore()
      this.PayPlanStore.onUpdateErrorMsg = this.onUpdateErrorMsg
    }
    this.timer = new Date().getTime()
    this.startTime = new Date().getTime()
    this.aiUsedCount = 0
  }

  componentWillMount() {
    const { bus = new MessageCenter(), dataSource, userInfo, originSpecificationGroupsList } = this.props
    bus.watch('check:value:changed', this.handleCheckValueChange)
    bus.watch('element:ref:select:staff', this.fnSelectStaff)
    bus.watch('element:ref:select:staffs', this.fnSelectStaffs)
    bus.watch('element:ref:select:payee', this.fnSelectPayee)
    bus.watch('save:bill:click', this.handleSaveBill)
    bus.watch('submit:bill:click', this.handleSubmitBill)
    bus.watch('element:details:line:click', this.fnDetailsLineClick)
    bus.watch('element:details:add:click', this.fnDetailsAddClick)
    bus.watch('element:details:import:click', this.fnDetailsImportClick)
    bus.watch('element:details:import:ocr:click', this.fnDetailsImportOCRClick)
    bus.watch('element:details:input:import:click', this.fnDetailsInputImportClick)
    bus.watch('element:details:aliPay:import:click', this.fnDetailsAliPayImportClick)
    bus.watch('element:details:import:afp:click', this.fnDetailsAifapiaoImportClick)
    bus.watch('element:details:importCSC:click', this.fnDetailsImportCSCClick)
    bus.watch('element:details:record:expends:click', this.fnDetailsRecordExpendsClick)
    bus.watch('element:details:quick:expends:click', this.fnDetailsQuickExpendsClick)
    bus.watch('element:details:importRequistition:click', this.fnDetailsImportRequistitionClick)
    bus.watch('element:details:batch:apportion', this.fnDetailBatchApportion)
    bus.watch('element:trips:addTrip', this.fnAddTrips)
    bus.watch('import:detail:excel', this.handleImportDetailByExcel)
    bus.watch('modify:bill:click', this.handleModifyBill)
    bus.watch('element:select:dataLink', this.handleSelectDataLink)
    bus.watch('clear:realted:application:details', this.clearRelatedApplication)
    bus.watch('update:select:expenselink', this.initRelatedApplication)
    bus.watch('update:visibleFeeTypes', this.updateVisibleFeeTypes)
    bus.watch('timeField:change', this.handleChangeTimeField)
    bus.watch('get:enableDraftConfig:value', this.getEnableDraftConfig)
    api.watch('get:bills:value', this.handleGetBillsValue)
    api.watch('get:bills:isQuickExpends', this.handleSendBillsIsQuickExpends)
    api.watch('generate:DetailByAutoCalculate', this.generateDetailByAutoCalculate)
    bus.watch('handleBillInfoModifySave', this.handleModifySaveCallBack)
    api.on('update:editedDetailList', this.handleUpdateList)
    api.on('external:FORM:edit', this.resetFieldsExternalsData)
    api.on('external:details:delete', this.delDetailsExternalsData)
    bus.on('HAB:value:changed', this.handleHABValueChange)
    bus.on('dynamic:value:changed', this.handleDynamicValueChange)
    bus.on('dynamic:value:blur', this.handleDynamicValueBlur)
    bus.on('set:delegator', this.fnSetDelegator)
    bus.on('details:change', this.onDetailsChange)
    bus.on('writtenOff:changed', this.handleWrittenOffChange)
    bus.on('payeeInfo:isMultiplePayee', this.saveMultiplePayeed)
    bus.on('check:requisition:detail', this.handleCheckRequisitionDetail)
    bus.on('element:datalink:card:click', this.fnHandleDataLinkCardClick)
    bus.on('update:apply:from:Expense', this.updateApplyFromExpense)
    bus.on('update:calculate:template', this.updateCalTemplate)
    bus.on('currency:money:select:change', this._handlerCurrencyMoneySelectChange)
    bus.on('dimention:currency:change', this._handleDimentionCurrencyChange)
    bus.on('switch:message:change', this.handleSwitchMessageChange)
    bus.watch('update:flow:risk:info', this.loadFlowRiskInfo)
    bus.on('billInfoEditable:expenseLinkDetails:change', this.handleExpenseLinkDetailsChange)
    bus.on('billInfoEditable:writtenOff:change', this.handleWrittenOffChangeFromBus)
    bus.on('dateRange:changed:value', this.handleDataRangeChange)
    bus.on('save:travelTemplate', this.handleSaveTravelTemplate)
    bus.on('right:risk:open:details', this.onOpenOwnerLoanList)
    bus.on('ai:summary:button:click', this.handleAISummaryButtonClick)
    api.dataLoader('@common.isHaveOfficialCardPower').load()
    api.dataLoader('@common.department').load()
    api.dataLoader('@common.feetypes').reload()
    api.invokeService('@common:get:default:payee')
    api.invokeService('@common:get:field:map', 'requisition:expense')
    api.invokeService('@tpp-v2:get:mappingRelation')
    api.invokeService('@tpp-v2:get:registrationInfo')
    api.invokeService('@tpp-v2:get:orderBaseProperties')
    // api.invokeService('@bills:get:association:group:data')
    api.dataLoader('@common.delegators').load()
    this.getNodesAIAgentMap()

    let { form = {}, delegator, id, formType } = dataSource
    let submitterId = delegator || form?.submitterId || userInfo?.staff
    api.invokeService('@bills:set:submitter:data', submitterId)
    if (submitterId) {
      api.invokeService('@common:get:lastChoice', submitterId.id)
      //天阳(bnB3nHI6Fb3qzw)的不调用了下面的接口
      if (Fetch.ekbCorpId !== 'bnB3nHI6Fb3qzw') {
        api.invokeService('@common:get:visibility:staffs', submitterId.id)
      }
      api.invokeService('@common:get:visibility:department:tree', submitterId.id)
    }
    api.dataLoader('@common.specificationVersionConfig').load()
    api.dataLoader('@common.allCurrencyRates').load()
    if (id) {
      api.invokeService('@bills:get:bill:notes', { flowId: id })
      formType === 'requisition' && api.invokeService('@bills:get:getTravelBackInfo', id, submitterId.id)
      form.code &&
        formType === 'requisition' &&
        api.invokeService('@itinerary-manage:get:travel:byRequisionCode', form.code)
      form?.details?.length > 0 && api.invokeService('@bills:get:getFeeTypeChange', id)
      IS_ZJZY && window.localStorage.setItem('bill_code', form?.code || '')
    }

    if (IS_ZJZY && !id) window.localStorage.setItem('bill_code', '')
    if (enableNewBillOptimization() && originSpecificationGroupsList?.length) {
      api
        .invokeService('@custom-specification:get:specificationGroups:byTypeV2', {
          type: formType,
          specificationGroupsList: originSpecificationGroupsList
        })
        .then(() => {
          this.setInitState(this.props, false)
        })
    } else {
      api.invokeService('@custom-specification:get:specificationGroups:byType', { type: formType }).then(() => {
        this.setInitState(this.props, false)
      })
    }
    this.initRelatedApplication(form)
    this.initRelatedMap(form)
    this.__handleInsertAssist('查看' + get(form, 'title') + '单据') // @i18n-ignore
    this._enableDraftConfig(this.props)
  }

  componentWillUnmount() {
    const {
      bus,
      dataSource: { id }
    } = this.props
    const { currentSpecification = {} } = this.state
    bus.un('check:value:changed', this.handleCheckValueChange)
    bus.un('HAB:value:changed', this.handleHABValueChange)
    bus.un('dynamic:value:changed', this.handleDynamicValueChange)
    bus.un('dynamic:value:blur', this.handleDynamicValueBlur)
    bus.un('set:delegator', this.fnSetDelegator)
    bus.un('element:ref:select:staff', this.fnSelectStaff)
    bus.un('element:ref:select:staffs', this.fnSelectStaffs)
    bus.un('element:ref:select:payee', this.fnSelectPayee)
    bus.un('element:details:line:click', this.fnDetailsLineClick)
    bus.un('element:details:add:click', this.fnDetailsAddClick)
    bus.un('element:details:import:click', this.fnDetailsImportClick)
    bus.un('element:details:import:ocr:click', this.fnDetailsImportOCRClick)
    bus.un('element:details:import:afp:click', this.fnDetailsAifapiaoImportClick)
    bus.un('element:details:importCSC:click', this.fnDetailsImportCSCClick)
    bus.un('element:details:record:expends:click', this.fnDetailsRecordExpendsClick)
    bus.un('element:details:quick:expends:click', this.fnDetailsQuickExpendsClick)
    bus.un('element:details:aliPay:import:click', this.fnDetailsAliPayImportClick)
    bus.un('element:trips:addTrip', this.fnAddTrips)
    bus.un('save:bill:click', this.handleSaveBill)
    bus.un('submit:bill:click', this.handleSubmitBill)
    bus.un('element:details:batch:apportion', this.fnDetailBatchApportion)
    bus.un('details:change', this.onDetailsChange)
    bus.un('writtenOff:changed', this.handleWrittenOffChange)
    bus.un('import:detail:excel', this.handleImportDetailByExcel)
    bus.un('modify:bill:click', this.handleModifyBill)
    bus.un('element:details:input:import:click', this.fnDetailsInputImportClick)
    bus.un('element:details:importRequistition:click', this.fnDetailsImportRequistitionClick)
    bus.un('element:select:dataLink', this.handleSelectDataLink)
    bus.un('payeeInfo:isMultiplePayee', this.saveMultiplePayeed)
    bus.un('check:requisition:detail', this.handleCheckRequisitionDetail)
    api.un('external:FORM:edit', this.resetFieldsExternalsData)
    api.un('external:details:delete', this.delDetailsExternalsData)
    api.un('get:bills:value', this.handleGetBillsValue)
    bus.un('element:datalink:card:click', this.fnHandleDataLinkCardClick)
    api.un('update:editedDetailList', this.handleUpdateList)
    api.un('generate:DetailByAutoCalculate', this.generateDetailByAutoCalculate)
    bus.un('update:visibleFeeTypes', this.updateVisibleFeeTypes)
    bus.un('clear:realted:application:details', this.clearRelatedApplication)
    bus.un('update:select:expenselink', this.initRelatedApplication)
    bus.un('update:apply:from:Expense', this.updateApplyFromExpense)
    bus.un('update:calculate:template', this.updateCalTemplate)
    bus.un('currency:money:select:change', this._handlerCurrencyMoneySelectChange)
    bus.un('dimention:currency:change', this._handleDimentionCurrencyChange)
    bus.un('switch:message:change', this.handleSwitchMessageChange)
    bus.un('update:flow:risk:info', this.loadFlowRiskInfo)
    bus.un('billInfoEditable:expenseLinkDetails:change', this.handleExpenseLinkDetailsChange)
    bus.un('billInfoEditable:writtenOff:change', this.handleWrittenOffChangeFromBus)
    bus.un('dateRange:changed:value', this.handleDataRangeChange)
    bus.un('save:travelTemplate', this.handleSaveTravelTemplate)
    bus.un('timeField:change', this.handleChangeTimeField)
    bus.un('get:enableDraftConfig:value', this.getEnableDraftConfig)
    bus.un('right:risk:open:details', this.onOpenOwnerLoanList)
    bus.un('ai:summary:button:click', this.handleAISummaryButtonClick)
    api.invokeService('@bills:update:dimention:currency', null)
    decorators.removeListener('myBill')
    decorators.removeListener('homepage')
    this.clearHistoryCurrencyRates()
    leaveFlowPerformanceStatistics({ flowId: id, specification: currentSpecification })
  }

  handleAISummaryButtonClick = () => {
    this.aiUsedCount++
  }

  handleModifySaveCallBack = () => {
    let { dataSource } = this.props
    dataSource.billFeeForceValidation = true
  }

  getEnableDraftConfig = async () => {
    return this.state.isEnableDraftConfig
  }

  _enableDraftConfig = async props => {
    let res_draftConfig = await enableDraftConfig(props)
    this.setState({
      isEnableDraftConfig: res_draftConfig
    })
  }

  handleSaveTravelTemplate = travelTemplateList => {
    this.setState({ travelTemplateList })
  }

  handleCheckBillisQuickExpends = async () => {
    const { dataSource } = this.props
    const isQuickExpends = await checkQuickExpends(dataSource)
    this.setState({ isQuickExpends }, () => {
      if (api.has('change:bills:isQuickExpends')) {
        api.invoke('change:bills:isQuickExpends', isQuickExpends)
      }
    })
  }

  componentDidMount() {
    const {
      dataSource,
      riskData,
      privilegeId = '',
      bus,
      baseDataProperties,
      AutoGenerateFeeDetail,
      civilServiceCard
    } = this.props
    const { currentSpecification = {} } = this.state
    const details = get(dataSource, 'form.details', [])
    this.handleCheckBillisQuickExpends()
    if (details.length > 0) {
      const multiplePayeesMode = get(dataSource, 'form.multiplePayeesMode', false)
      const payPlanMode = get(dataSource, 'form.payPlanMode', false)
      const payPlan = get(dataSource, 'form.payPlan', [])
      if (!riskData) {
        api.invokeService('@bills:get:flow:risk:warning', { id: dataSource.id, privilegeId }).then(_riskData => {
          if (_riskData.value) {
            const riskData = polyfillForAIRiskData(_riskData)
            const template = this.getTemplate(dataSource, baseDataProperties)
            const riskWarning = parseFlowRiskV2(riskData, details).form
            const extraRiskWarningList = getExtraRiskWarningList(template, riskWarning, multiplePayeesMode)
            const riskInTemplateList = getRiskInTemplate(template, riskWarning)
            const riskFieldNum = riskData?.riskWarningV2?.length // getRiskFieldNum(riskWarning) 改用新版的数量
            riskWarning && bus?.setFieldsExternalsData?.({ ...riskWarning })
            this.setState({
              riskData: { ...riskData, isForbid: false },
              riskWarning,
              extraRiskWarningList,
              riskInTemplateList,
              riskFieldNum
            })
          }
        })
      }
      if (multiplePayeesMode && !payPlanMode && payPlan.length === 0) {
        this.onDetailsChange(details)
      }
    }
    //自动生成费用明细
    if (AutoGenerateFeeDetail) {
      this.getAutoGenerationFeeRules(dataSource)
    }
    if (civilServiceCard) {
      getOfficialCardSetting().then(res => {
        const referenceDataType = get(res, 'value.referenceDataType', {})
        this.setState({ referenceDataType })
      })
    }
    // @todo 异地单据寄收管理 校验
    if (this.props.ExpressCharge) {
      this.getReceiveExceptionBill()
      this.getExpressConfigAbout()
    }
    this.loadFlowRiskInfo()
    this.loadSetCurrencyRates()
    flowDetailsStatistics({
      formType: dataSource?.formType,
      state: dataSource?.state
    })
    reportBillPagePaintDuration()
  }

  loadSetCurrencyRates = () => {
    const { allCurrencyRates, billsRateList } = this.props
    if (!billsRateList?.length && allCurrencyRates?.length) {
      api.invokeService('@bills:update:currencyRatesList', { items: allCurrencyRates })
    }
  }

  loadFlowRiskInfo = async newDetails => {
    const { dataSource, baseDataProperties, bus } = this.props
    let form = get(dataSource, 'form', {})
    if (newDetails) {
      const data = await bus.getValue()
      form = { ...form, ...data, details: newDetails }
    }
    const currentSpecification =
      get(dataSource, 'currentSpecification') ||
      get(dataSource, 'form.specificationId') ||
      get(dataSource, 'requisitionInfo.defaultSpecification')
    const components = currentSpecification?.components || []
    const detailCmp = components.find(el => el.field === 'details')

    if (
      detailCmp?.realtimeCalculateBudget &&
      !!form?.details?.length &&
      ['draft', 'rejected', 'modify', 'new', undefined].includes(dataSource.state)
    ) {
      form.details.forEach(detail => {
        if (detail?.feeTypeForm && !detail.feeTypeForm.detailId) {
          detail.feeTypeForm.detailId = uuid(14)
        }
      })
      this.tempId = new Date().getTime()
      const currentValue = parseFormValueAsParam(form, currentSpecification, dataSource, baseDataProperties) || {}
      api
        .invokeService('@bills:get:flow:risk:info', {
          formType: dataSource.formType,
          form: currentValue.form,
          state: dataSource.state || 'new',
          version: this.tempId
        })
        .then(riskData => {
          if (this.tempId.toString() === riskData.flowId) {
            const ShowRiskInfoBillStates = ['draft', 'rejected']
            const tg_show_apportion_risk_in_edit = api.getState()['@common'].toggleManage?.[
              'tg_show_apportion_risk_in_edit'
            ]
            const parseApportionRiskData =
              tg_show_apportion_risk_in_edit && ShowRiskInfoBillStates.includes(dataSource?.state)
            const details = get(dataSource, 'form.details', [])
            const riskInfo = parseFlowRisk(riskData, parseApportionRiskData, details).form?.details
            if (riskInfo) {
              this.setState({
                riskInfo
              })
            }
          }
        })
      return Promise.resolve(form.details)
    }
    return Promise.resolve(newDetails)
  }

  // 清空时间字段对应的汇率历史版本
  clearHistoryCurrencyRates = () => {
    return api.invokeService('@bills:save:history:currency:rates')
  }

  getTimeField = async specification => {
    const currencyConfig = await api.dataLoader('@common.currencyConfig').load()
    const { components = [] } = specification
    const originalId =
      typeof specification?.originalId === 'object' ? specification?.originalId?.id : specification?.originalId
    const active = get(currencyConfig, 'timeCaliber.active', false)
    const rules = getV(currencyConfig, 'timeCaliber.rules', [])
    if (active) {
      return rules.find(el => el.specificationIds.includes(originalId))?.timeField
    }
    return undefined
  }

  // 根据时间获取对应时期的本位币所对应的汇率
  handleChangeTimeField = async ({ currencyNumCode, billData, date }) => {
    const { bus, dimentionCurrencyInfo } = this.props
    const { timeField } = this.state
    if (!timeField) return
    bus.emit('savebtn:state:change', { disabled: true })
    const billValue = await bus.getFieldsValue()
    const data = billData || billValue || {}
    const time = date || data[timeField]
    if (time) {
      let originalId = currencyNumCode
      if (!originalId) {
        const standardCurrency = dimentionCurrencyInfo?.currency || api.getState()['@common']?.standardCurrency
        originalId = standardCurrency?.numCode
      }
      const endOfDay = moment(time)
        .endOf('day')
        .valueOf() // 获取当天最后一秒的时间戳

      const historyCurrencyInfo = await getEffectiveCurrencyInfo({ originalId, time: endOfDay }).catch(err => {
        console.log(err)
        bus.emit('savebtn:state:change', { disabled: false })
      })
      const historyCurrency = historyCurrencyInfo?.items
      await api.invokeService('@bills:save:history:currency:rates', { currency: originalId, rates: historyCurrency })
      bus.emit('set:payeeInfo:history:currency:rate', originalId, historyCurrency)
      bus.emit('set:history:currency:rate', historyCurrency)
      if (data?.details?.length && getBoolVariation('bill-fee-validate-rate')) {
        const details = cloneDeep(data.details)
        let shouldRefreshDetails = false
        details.forEach(el => {
          const feeTypeComponents = el?.specificationId?.components || []
          const moneyFieldKeys = []
          feeTypeComponents.forEach(el => {
            if (el.type === 'money' && !el?.editable) {
              moneyFieldKeys.push(el.field)
            }
          })
          moneyFieldKeys.forEach(key => {
            const moneyFieldValue = el?.feeTypeForm?.[key]
            if (moneyFieldValue && (moneyFieldValue.rate || moneyFieldValue.budgetRate)) {
              let historyCurrencyItem, budgetCurrencyItem
              historyCurrency.forEach(el => {
                if (!historyCurrencyItem && el.numCode === moneyFieldValue.foreignNumCode) historyCurrencyItem = el
                if (!budgetCurrencyItem && el.numCode === moneyFieldValue.budgetNumCode) budgetCurrencyItem = el
              })
              if (
                (historyCurrencyItem && historyCurrencyItem.rate !== moneyFieldValue.rate) ||
                (budgetCurrencyItem && budgetCurrencyItem.budgetRate !== moneyFieldValue.budgetRate)
              ) {
                el.shouldSaveFeetype = true
                shouldRefreshDetails = true
              }
            }
          })
        })
        if (shouldRefreshDetails) {
          bus.setFieldsValue({ details })
        }
      }
    } else {
      await this.clearHistoryCurrencyRates()
      this.checkDetailsRates(data)
    }
    bus.emit('savebtn:state:change', { disabled: false })
  }

  checkDetailsRates = async data => {
    const { bus } = this.props
    // 取汇率
    const standardCurrency = await api.getState()['@common']?.standardCurrency
    const dimentionCurrency = api.getState()['@bills']?.dimentionCurrencyInfo
    const allCurrencyRates = await api.dataLoader('@common.allCurrencyRates').reload()
    const allCurrency = allCurrencyRates.filter(i => i.originalId === standardCurrency?.numCode)
    const rates = dimentionCurrency?.rates || allCurrency || []
    await api.invokeService('@bills:save:history:currency:rates', { currency: standardCurrency?.numCode, rates: rates })
    bus.emit('set:history:currency:rate', rates)
    if (data?.details?.length && getBoolVariation('bill-fee-validate-rate')) {
      const details = cloneDeep(data.details)
      let shouldRefreshDetails = false
      details.forEach(el => {
        const feeTypeComponents = el?.specificationId?.components || []
        const moneyFieldKeys = []
        feeTypeComponents.forEach(el => {
          if (el.type === 'money' && !el?.editable) {
            moneyFieldKeys.push(el.field)
          }
        })
        moneyFieldKeys.forEach(key => {
          const moneyFieldValue = el?.feeTypeForm?.[key]
          if (moneyFieldValue.rate || moneyFieldValue.budgetRate) {
            let historyCurrencyItem, budgetCurrencyItem
            rates.forEach(el => {
              if (!historyCurrencyItem && el.numCode === moneyFieldValue.foreignNumCode) historyCurrencyItem = el
              if (!budgetCurrencyItem && el.numCode === moneyFieldValue.budgetNumCode) budgetCurrencyItem = el
            })
            if (
              (historyCurrencyItem && historyCurrencyItem.rate !== moneyFieldValue.rate) ||
              (budgetCurrencyItem && budgetCurrencyItem.budgetRate !== moneyFieldValue.budgetRate)
            ) {
              el.shouldSaveFeetype = true
              shouldRefreshDetails = true
            }
          }
        })
      })
      if (shouldRefreshDetails) bus.setFieldsValue({ details })
    }
  }

  handleSendBillsIsQuickExpends = () => {
    const { isQuickExpends } = this.state
    return isQuickExpends
  }

  handleDataRangeChange = async date => {
    if (this.props.KA_NUMBER_FIELD_COUNT_RULE_2) {
      // charge 自动计算，按计算半日规则计算 需日期范围变更后触发自动计算
      const billData = cloneDeep(await this.props.bus.getValue())
      this.startAutoCalc2({ ...billData, ...date })
    }
  }

  getReceiveExceptionBill = dataSourceP => {
    if (!dataSourceP) {
      dataSourceP = this.props.dataSource || {}
    }
    const originalId = get(
      dataSourceP,
      'form.specificationId.originalId.id', // 复制或者草稿
      get(
        dataSourceP,
        'form.specificationId.originalId', // 更新模板
        get(
          dataSourceP,
          'currentSpecification.originalId', // 正常新建报销单据
          get(dataSourceP, 'requisitionInfo.defaultSpecification.originalId', null) // 通过申请单创建报销单据
        )
      )
    )

    if (this.submitControl.originalId !== originalId) {
      // 如果新拿到的 originalId 和 之前的不一样，则查询
      const params = {
        start: 0,
        filters: `(state ==\"receivingExcep\")&&(form.specificationId.id.containsIgnoreCase("${originalId}"))`
      }
      searchMyBillList(params).then(resp => {
        const { items = [] } = resp
        this.submitControl.originalId = originalId
        this.submitControl.receivingExceptData = (items || []).map(v => v?.form?.code)
      })
    }
  }

  getExpressConfigAbout = () => {
    const params = {
      limit: {
        start: 0,
        count: 1 //只需要一条，没有表示没有
      },
      select: '`...`', //查所有字段
      filterBy: 'type=="POWER_110220_SETTING"' //固定拼接格式POWER_110220_SETTING
    }
    api.dispatch(searchExtensionCenterConfig(params)).then(resp => {
      const { count = 0, items = [] } = resp
      if (count > 0) {
        const {
          configDetail: { submitControl: { isSubmit = true, allowMessage = '', forbidMessage = '' } = {} } = {}
        } = items[0]
        this.submitControl.hasConfig = true
        this.submitControl.isSubmit = isSubmit
        this.submitControl.tipsMessage = isSubmit ? allowMessage : forbidMessage
      }
    })
  }

  //获取当前模板里自动生成费用明细的监听字段
  getAutoGenerationFeeRules = async dataSource => {
    let specId =
      dataSource?.form?.specificationId?.id ||
      dataSource?.currentSpecification?.id ||
      dataSource?.requisitionInfo?.defaultSpecification?.id
    api.dispatch(getAutoGenerationFeeDetailRules({ specId })).then(res => {
      let generationRule = res?.value?.generationRule || []
      let generationField = []
      generationRule.forEach(val => {
        generationField.push(val.field)
      })
      this.setState({ generationField, generationRule })
    })
  }

  handleInvoiceDisable = list => {
    const { disableInvoiceList = {} } = this.state
    this.setState({ disableInvoiceList: { ...disableInvoiceList, ...list } })
  }

  handleExpenseLinkDetailsChange = ({ expenseLinkDetails }) => {
    this.expenseLinkDetails = expenseLinkDetails
  }

  handleWrittenOffChangeFromBus = writtenOff => {
    this.writtenOffFromEventBus = writtenOff
  }

  __handleInsertAssist = title => {
    api.invokeService('@common:insert:assist:record', {
      title
    })
  }

  getTemplate = (dataSource, baseDataProperties) => {
    const { flowRulePerformLogs } = dataSource
    let template = parseAsFormTemplate(dataSource, baseDataProperties)
    template = parseAsReadOnlyFormTemplate(template)
    return presetFormulaValue(flowRulePerformLogs && flowRulePerformLogs.results, template)
  }

  clearRelatedApplication = () => {
    this.setState({ applicationListDetails: [] })
  }

  initRelatedApplication = value => {
    let map = {}
    if (this.copyDetials && this.copyDetials.length) {
      map = this.getDetailsRelateMoney()
    }
    return this.initApplicationDetails(value, map)
  }

  getDetailsRelateMoney = () => {
    const map = {}
    this.copyDetials.forEach(line => {
      const {
        feeTypeForm: { linkDetailEntities }
      } = line
      if (linkDetailEntities) {
        linkDetailEntities.forEach(link => {
          const { amount } = link
          const id = get(link, 'linkDetailEntityId.id')
          if (id) {
            map[id] = new MoneyMath(map[id]).add(amount).value
          }
        })
      }
    })
    return map
  }

  initRelatedMap = form => {
    const data = enableFlowOptimization() ? form : cloneDeep(form)
    const details = get(data, 'details', []) || []
    details.forEach(line => {
      const {
        feeTypeForm: { detailId, linkDetailEntities }
      } = line
      if (linkDetailEntities && linkDetailEntities.length) {
        const arr = linkDetailEntities.map(line => {
          const { amount, linkDetailEntityId } = line
          return { consumeAmount: amount, relateId: linkDetailEntityId.id }
        })
        related.setRelatedMap({ id: detailId, value: enableFlowOptimization() ? cloneDeep(arr) : arr })
      }
    })
  }

  initApplicationDetails = async (data, map) => {
    const expenseLinks = get(data, 'expenseLinks', []) || []
    const ids = expenseLinks.map(v => v.id)
    if (!ids.length) return
    const dataList = await api.invokeService('@bills:get:related:detail:list', { ids }).then(async value => {
      const items = get(value, 'items', [])
      for (let i = 0; i < items.length; i++) {
        for (let index = 0; index < items[i].dataList.length; index++) {
          const line = items[i].dataList[index]
          const money = map[line.id] ? map[line.id] : 0
          const values = await getObjById({ ...line }, line.feeTypeId?.requisitionSpecificationId?.components || [])
          line.feeTypeForm = { ...line, ...values }
          line.unwrittenOffAmount = new MoneyMath(line.unwrittenOffAmount).add(money).value
        }
      }
      this.setState({ applicationListDetails: items })
      return items
    })

    return dataList
  }

  handleUpdateList = detailId => {
    if (this.editedDetailList.indexOf(detailId) < 0) {
      this.editedDetailList.push(detailId)
    }
  }

  fnHandleDataLinkCardClick = ({ entityInfo, field, showClose, disabledStaff = false }) => {
    let { isModal, stackerManager } = this.props
    const { ability = '', name = '' } = field
    if (ability === 'contractSettle') {
      disabledStaff = true
    }
    if (getTrueKey(name) === 'relationContract') {
      disabledStaff = true
    }
    if (isModal) {
      stackerManager.push('DataLinkDetailModal', {
        entityInfo: { ...entityInfo, entityId: field.referenceData },
        disabledStaff
      })
    } else {
      api.open('@bills:DataLinkDetailModal', {
        viewKey: 'DataLinkDetailModal',
        entityInfo: { ...entityInfo, entityId: field.referenceData, disabledStaff },
        field,
        showClose
      })
    }
  }

  handleCheckRequisitionDetail = value => {
    // 选中关联申请单后
    let { detail, flowId } = value
    let { isModal, stackerManager } = this.props
    if (isModal) {
      return stackerManager.push('ApplyEventDetail', {
        detail,
        flowId
      })
    } else {
      return api.open('@bills:ApplyEventStackerModal', {
        viewKey: 'ApplyEventDetail',
        detail,
        flowId
      })
    }
  }

  componentWillReceiveProps(nextProps) {
    if (!isEqual(this.props.dataSource, nextProps.dataSource) || this.props.riskData !== nextProps.riskData) {
      logEvent('单据发生变化', nextProps.dataSource)
      return this.setInitState(nextProps, true)
    }
    if (this.props.lastChoice !== nextProps.lastChoice) {
      this.forceUpdate()
    }
  }

  onUpdateErrorMsg = () => {
    const objs = toJS(this.PayPlanStore.objs)
    const { currentSpecification } = this.state
    const errorLength = objs.filter(payPlan => !payPlan.payeeInfo).length
    if (!!errorLength) {
      return i18n.get('请选择收款信息。')
    }
    if (objs.length === 0) {
      let errorMsg = i18n.get('请添加支付计划。')
      // 支付金额不为0时收款信息必填
      const pay = currentSpecification.configs.find(v => v.ability === 'pay')
      const targetTotalMoney = Number(this.PayPlanStore?.targetTotalMoney || 0)
      if (pay && pay?.optionalPayeeByZero && targetTotalMoney === 0) {
        errorMsg = null
      }
      return errorMsg
    }
    if (Number(this.PayPlanStore.totalMoney) - Number(this.PayPlanStore.targetTotalMoney) < 0) {
      return i18n.get('已计划支付金额 < 总支付金额，请修改。')
    }
    if (Number(this.PayPlanStore.totalMoney) - Number(this.PayPlanStore.targetTotalMoney) > 0) {
      const { supportPlanByApportion, paymentPlanByApportion } = this.PayPlanStore
      if (supportPlanByApportion && paymentPlanByApportion) {
        return i18n.get('已计划支付金额>总支付金额，请将收款方式调整至「按金额」后修改。')
      }
      return i18n.get('已计划支付金额 > 总支付金额，请修改。')
    }
    document.getElementsByClassName('layout5-content')[0].scrollTop = 100
  }

  //设置是否多收款人值到 redux树上
  saveMultiplePayeed = mltiplePayeeObj => {
    const { multiplePayeesMode, payPlanMode } = mltiplePayeeObj
    const { saveMultiplePayeed, bus } = this.props
    const { currentSpecification } = this.state
    saveMultiplePayeed(mltiplePayeeObj)
    bus.getValue?.().then(formValue => {
      const details = formValue?.details
      const isHaveDetails = details?.length > 0
      if (!isHaveDetails) return
      fnCheckCompleted({ value: details, billSpecification: currentSpecification, mltiplePayeeObj })
      if (!multiplePayeesMode || (multiplePayeesMode && payPlanMode)) {
        details.forEach(item => {
          const { specificationId } = item
          const feeDetailPayeeId = specificationId?.components?.find(cp => cp?.field === 'feeDetailPayeeId')
          if (!feeDetailPayeeId) {
            delete item.feeTypeForm?.feeDetailPayeeId
          }
        })
      }
      this.onDetailsChange(details)
    })
  }

  // 设置费用明细的可选值
  setFeeDetailOptional(currentSpecification) {
    const receipt = currentSpecification.configs.find(v => v.ability === 'receipt')
    const isReceipt = (receipt?.summarySelect == 2 && receipt?.feeDetailNotRequired) || false
    const feeType = currentSpecification?.components.find(v => v.field == 'details')
    // 单据为收款金额并且非必填项 重置 optional 字段 为可选 summarySelect == 2 表示为还款金额类型，还款金额并且非必填
    if (isReceipt && feeType) {
      feeType.optional = true
    }
  }

  getCurrentSpecification = () => {
    const { dataSource, specificationGroupsList } = this.props
    let currentSpecification = dataSource.currentSpecification || dataSource.requisitionInfo?.defaultSpecification
    if (!currentSpecification) {
      //编辑
      currentSpecification = getDefSpecificationDS(dataSource, specificationGroupsList)
      if (!currentSpecification) {
        return
      }
    }
    return currentSpecification
  }

  setInitState(props, isPropsChange) {
    const {
      bus,
      dataSource,
      userInfo,
      customizeQueryPower,
      isModify,
      currentNode,
      AutoGenerateFeeDetail
    } = props
    let { riskData = {} } = props
    this.visibleSpecificationList = this.getSpecificationGroupsList()
    const currentSpecification = this.getCurrentSpecification()
    if (!currentSpecification) return

    this.initHiddenFiled(currentSpecification, true)
    this.initTimeField(currentSpecification, true)

    api
      .dataLoader('@common.globalFields')
      .load()
      .then(async resp => {
        let baseDataProperties = resp.data
        //新建或者从申请事项进入
        this.setFeeDetailOptional(currentSpecification)
        if (bus.has('bills:check:nulllify:rule')) {
          bus.invoke('bills:check:nulllify:rule', { id: dataSource.id, state: dataSource.state })
        }
        const isRemuneration = checkIsRemuneration(currentSpecification)
        isRemuneration && fixRemunerationSpecification(currentSpecification)
        let template = parseAsMeta(currentSpecification, baseDataProperties)
        if (isModify) {
          template = parseApproveModifyLimitField(currentNode, template)
        }
        template = await this.initAttachmentField(template)
        //由申请事项发起的报销重新计算模版信息
        if (dataSource.requisitionInfo) {
          let { requisition, visibleSpecificationList, withNotes, detailList = [] } = dataSource.requisitionInfo
          this.visibleSpecificationList = visibleSpecificationList
          let form = {}
          if (withNotes) {
            //随手记发起报销
            form.details = withNotes
          } else {
            //申请单发起报销
            let expenseLinkComponent = template.find(v => v.name === 'expenseLink' || v.name === 'expenseLinks')
            let linkRequisitionInfoComponent = template.find(v => v.name === 'linkRequisitionInfo')
            let submitterComponent = template.find(v => v.name === 'submitterId')
            let legalEntityMultiCurrency = template.find(v => v.name === 'legalEntityMultiCurrency')
            let legalEntityMultiCurrencyData = ''
            if (!!detailList?.length) {
              const legalEntityMultiCurrencyList = await api.invokeService('@common:get:staff:dimension', {
                name: 'basedata.Dimension.法人实体'
              })
              const items = legalEntityMultiCurrencyList?.items || []
              const requisitionCurrencyInfo = detailList[0]
              const legalEntityMultiCurrency = get(requisitionCurrencyInfo, 'form.legalEntityMultiCurrency')
              if (legalEntityMultiCurrency?.name) {
                legalEntityMultiCurrencyData = legalEntityMultiCurrency
              } else {
                legalEntityMultiCurrencyData = items.find(v => v.id === legalEntityMultiCurrency)
              }
            }
            if (submitterComponent) {
              submitterComponent.editable = false //发起报销时不允许更改委托人
            }
            if (expenseLinkComponent) {
              this.expenseLink = true
              const field = expenseLinkComponent.field
              const data = field === 'expenseLink' ? requisition : [requisition]
              form[field] = data
            }
            if (linkRequisitionInfoComponent) {
              this.linkRequisitionInfo = true
              form.linkRequisitionInfo = requisition
            }
            if (legalEntityMultiCurrency) {
              this.legalEntityMultiCurrencyInfo = true
              legalEntityMultiCurrency.editable = true
              form.legalEntityMultiCurrency = legalEntityMultiCurrencyData
            }
          }
          dataSource.form = dataSource.form ? { ...dataSource.form, ...form } : form
        }
        const value = parseAsFormValue(dataSource, true)
        const mltiplePayeeObj = {
          multiplePayeesMode: value.multiplePayeesMode ?? false,
          payPlanMode: value.payPlanMode ?? false,
          payeePayPlan: value.payeePayPlan ?? false
        }
        this.saveMultiplePayeed(mltiplePayeeObj)
        let { writtenOff = {}, form = {}, delegator, loanManualRepayment = {} } = dataSource
        let { records = [] } = writtenOff
        let repaymentRecords = loanManualRepayment?.records || []
        let submitterId = delegator || form.submitterId || userInfo.staff
        let isHiddenRiskWarning = false // !!~['modify'].indexOf(dataSource.state)
        this.fnGetApplyByExpense(currentSpecification, value.expenseLink || value.expenseLinks, submitterId)
        const details = get(dataSource, 'form.details', [])
        riskData = polyfillForAIRiskData(riskData)
        const formRiskWarningData = parseFlowRiskV2(riskData, details).form || {}
        const extraRiskWarningList = getExtraRiskWarningList(template, formRiskWarningData)
        const riskInTemplateList = getRiskInTemplate(template, formRiskWarningData)
        const riskFieldNum = isHiddenRiskWarning ? 0 : riskData?.riskWarningV2?.length || 0
        // 获取报销单手动还款组件的可见性
        const isAutoRepayment = get(
          get(currentSpecification, 'configs', []).find(v => v.ability === 'allowRepayment'),
          'isAllowedRepayment',
          false
        )
        let [timeField, hiddenFields] = await Promise.all([
          this.initTimeField(currentSpecification, false),
          this.initHiddenFiled(currentSpecification, false)
        ])

        this.setState(
          {
            template,
            hiddenFields: hiddenFields ?? [],
            currentSpecification,
            value,
            writtenOffRecords: records,
            submitterId,
            formRiskWarningData,
            extraRiskWarningList,
            riskFieldNum,
            riskInTemplateList,
            riskData,
            isAutoRepayment,
            repaymentRecords,
            timeField
          },
          () => {
            // TODO: 这个回调里的setTimeout 很有问题，会导致自动计算顺序紊乱，临时缩短时间，后续要针对该处做优化
            // 如果初始化时，已经有明细了，自动触发明细修改事件
            if (value.details?.length) {
              // @todo 这里为了保证在表单已经渲染完成，等待了500ms，暂时没有更好的处理方案
              const timer0 = setTimeout(() => {
                this.onDetailsChange(value.details)
                clearTimeout(timer0)
              }, 100)
            }
            if (api.has('remuneration:creare:details')) {
              api.invoke('remuneration:creare:details')
            }
            const timer1 = setTimeout(() => {
              if (customizeQueryPower) {
                this.fnGetCalculateField(
                  submitterId,
                  false,
                  ['draft', 'rejected', 'modify'].includes(dataSource.state),
                  true
                )
              } else {
                this.fnGetCalculateField(submitterId, undefined, undefined, true)
              }
              bus?.has('bill:value:changed:forhab') && bus.emit('bill:value:changed:forhab')
              clearTimeout(timer1)
            }, 100)
            !isHiddenRiskWarning && bus?.setFieldsExternalsData?.({ ...formRiskWarningData })
            //触发自动生成费用明细的提交人字段
            if (AutoGenerateFeeDetail && dataSource.state === 'new') {
              const timer2 = setTimeout(() => {
                this.fnAutoGenerationFee(null, 'submitterId', this.state.currentSpecification)
                clearTimeout(timer2)
              }, 100)
            }
            this.handleChangeTimeField({
              currencyNumCode: value.legalEntityMultiCurrency?.form?.baseCurrencyId,
              billData: value
            })
            dataSource.billFeeForceValidation && this.initValidateByModify()
            this.initDataLinkPermissionFields()
            if (['new', 'draft', 'rejected'].includes(dataSource.state)) {
              !isPropsChange && this.fnCheckDelegater(submitterId, currentSpecification)
            }
            this.updateAttachmentField()
          }
        )
      })
  }

  async initAttachmentField(template) {
    if (enableNewBillOptimization()) {
      this.attachmentConfigPromise = fetchAttachmentConfig()
      return template
    }

    await fetchAttachmentConfig()
    return fnParseTemplateFields(template)
  }

  async initTimeField(currentSpecification, preInit = false) {
    const handleFiled = timeFieldParam => {
      if (!timeFieldParam && ['requisition', 'loan'].includes(currentSpecification.type)) {
        timeFieldParam = currentSpecification.type === 'loan' ? 'loanDate' : 'requisitionDate'
      }
      return timeFieldParam
    }
    if (enableNewBillOptimization() && preInit) {
      this.timeFieldPromise = await this.getTimeField(currentSpecification).then(handleFiled)
      this.setState({
        timeField: this.timeFieldPromise
      })
    }

    return enableNewBillOptimization()
      ? this.timeFieldPromise
      : await this.getTimeField(currentSpecification).then(handleFiled)
  }

  async initHiddenFiled(currentSpecification, preInit = false) {
    if (enableNewBillOptimization() && preInit) {
      this.hiddenFieldsPromise = await getSpecificationHiddenFields(currentSpecification)
      console.log('hiddenFields', this.hiddenFieldsPromise)
      this.setState({
        hiddenFields: this.hiddenFieldsPromise
      })
    }

    return enableNewBillOptimization()
      ? this.hiddenFieldsPromise
      : await getSpecificationHiddenFields(currentSpecification)
  }

  async updateAttachmentField() {
    if (this.attachmentConfigPromise) {
      await this.attachmentConfigPromise
      const { template } = this.state
      this.attachmentConfigPromise = null
      this.setState({
        template: fnParseTemplateFields(template)
      })
    }
    return {}
  }

  // 审批中修改自动定位错误字段
  initValidateByModify = debounce(() => {
    const { bus, isModify } = this.props
    const { currentSpecification } = this.state
    if (isModify && bus) {
      bus.getValueWithValidate?.(0).catch(e => {
        const components = currentSpecification.components
        const errors = getValidateErrorByShow(components, Object.keys(e))
        if (!errors.length) {
          setValidateError({ bill: Object.keys(e) })
        }
        return Promise.reject(e)
      })
    }
  }, 1000)

  // 获取业务对象依赖关系
  initDataLinkPermissionFields = async () => {
    const { currentSpecification } = this.state
    const items = await getDataLinkPermissionFieldComponent(currentSpecification?.id)
    this.setState({
      dependenceFieldOfDataLink: items
    })
  }

  // 业务对象字段唯一值时，自动赋值逻辑的内部处理函数
  _initAutoAssignOfOneResultOfDataLinkHandlerCore = (fields = []) => {
    let { bus, dataSource, baseDataProperties } = this.props
    let { currentSpecification } = this.state
    let components = currentSpecification?.components ?? []
    let curTime = new Date().getTime()
    this.timer = curTime

    //唯一值处理逻辑只在新建单据时做改为在编辑态和新建做
    if (dataSource && !['new', 'draft', 'rejected', 'modify'].includes(dataSource?.state)) return

    const dataLinkFields = filterDataLinkFields(components, fields)
    if (!dataLinkFields.length) return

    bus?.getValue()?.then(async data => {
      let currentValue = parseFormValueAsParam(data, currentSpecification, dataSource, baseDataProperties) || {}
      let flowId = dataSource?.id || ''
      const limit = 10
      currentValue.submitterId = getV(this.props, 'submitterId.id') || data?.submitterId.id
      currentValue.flowId = flowId
      showLoading()
      const res = await handleAutoAssignOfOneResultOfDataLink({ dataLinkFields, currentValue, limit })
      const checkValue = []
      const isEditBill = ['draft', 'rejected', 'modify'].includes(dataSource?.state)
      if (getBoolVariation('cyxq-74116-datalink-filter-auto-assign') || this.timer === curTime) {
        dataLinkFields.forEach((line, idx) => {
          const temp = res[idx]
          const { id } = data[line.field] || {}
          const isPreviousIdPresent = (temp || []).map(v => v?.dataLink?.id).includes(id) // 上一次存在的id是否在这次结果中
          const rangeOnlyOneAutomaticAssignment = useNewAutomaticAssignment()
            ? 'rangeOnlyOneAutomaticAssignment'
            : 'autoFillByFilterDataOnlyOne'

          if (temp && temp.length === 1 && line[rangeOnlyOneAutomaticAssignment]) {
            // 唯一值自动赋值
            bus.emit('on:autoAssignDataLink', { id: temp[0]?.dataLink?.id, field: line })
          }
          if (id && !isPreviousIdPresent && temp.length >= limit) {
            // 因返回数据存在翻页，这里记录id，后面统一找
            checkValue.push({ field: line.field, id: id })
            line.filterBy = `(id.in("${id}")) && (active==true)`
          } else if (
            (temp && temp.length !== 1 && !isPreviousIdPresent) ||
            (!line[rangeOnlyOneAutomaticAssignment] && !isPreviousIdPresent)
          ) {
            // 非编辑单据返回多条直接清空，编辑单据只有在筛选依赖条件变更后更新对应赋值结果（多条清空）
            if (!isEditBill || (isEditBill && !this.isInitAutoAssignOfOneResultOfDataLink)) {
              bus.emit('clear:autoAssignDataLink', { field: line })
            }
          }
        })
        // 确定数据删除
        if (checkValue.length) {
          let dataLinkFieldSameFields = dataLinkFields
          if (getBoolVariation('cyxq-74667')) {
            dataLinkFieldSameFields = dataLinkFields.filter(line => checkValue.some(v => v.field === line.field))
          }
          const res2 = await handleAutoAssignOfOneResultOfDataLink({
            dataLinkFields: dataLinkFieldSameFields,
            currentValue,
            filterBySearch: true
          })

          dataLinkFieldSameFields.forEach((line, idx) => {
            const temp = res2[idx]
            const { id } = data[line.field] || {}
            const isPreviousIdPresent = (temp || []).map(v => v?.dataLink?.id).includes(id) // 上一次存在的id是否在这次结果中

            if (!isPreviousIdPresent) {
              if (!isEditBill || (isEditBill && !this.isInitAutoAssignOfOneResultOfDataLink)) {
                bus.emit('clear:autoAssignDataLink', { field: line })
              }
            }
          })
        }
      }
      hideLoading()
      this.isInitAutoAssignOfOneResultOfDataLink = false
    })
  }

  // 创建防抖版本的处理函数
  _initAutoAssignOfOneResultOfDataLinkHandlerDebounced = debounce(
    this._initAutoAssignOfOneResultOfDataLinkHandlerCore,
    300
  )

  // 业务对象字段唯一值时，自动赋值逻辑的实际处理函数
  initAutoAssignOfOneResultOfDataLink = (fields = []) => {
    if (getBoolVariation('cyxq-75092')) {
      // 添加队列，调用不防抖的版本
      callFnByQueueNew({}, () => this._initAutoAssignOfOneResultOfDataLinkHandlerCore(fields))
    } else {
      // 使用防抖版本
      this._initAutoAssignOfOneResultOfDataLinkHandlerDebounced(fields)
    }
  }

  countBy = (arr, value) => {
    return (arr || []).filter(item => item === value).length
  }

  // 获取附件字段明细中是否配置发票类型控制
  getAttachmentOfInvoiceType = () => {
    const { currentSpecification } = this.state
    const components = currentSpecification?.components ?? []
    const obj = components.find(item => item?.field === 'attachments' && item?.checkDetailInvoiceType)
    return Boolean(obj)
  }

  onDetailsChange = data => {
    if (this.props.KA_NUMBER_FIELD_COUNT_RULE) {
      this.countRuleCheckFields.forEach(fieldRuleMap => {
        const { bus } = this.props
        const {
          fieldKey,
          countRule: { fieldValue, fieldType, operatorStr, isDistinct, countNum }
        } = fieldRuleMap
        let fieldsValues = data
          .map(dataItem => {
            // 如果是时间戳，转换成当天的起始时间戳
            if (fieldType && fieldType === 'date') {
              const value = dataItem.feeTypeForm[fieldValue]
              return value
                ? moment(value)
                  .startOf('day')
                  .unix()
                : undefined
            }
            return dataItem.feeTypeForm[fieldValue]
          })
          .filter(item => item !== undefined)
        fieldsValues = fieldsValues.filter(dataItem => {
          if (dataItem === undefined) {
            return false
          }
          switch (operatorStr) {
            case '=':
              return this.countBy(fieldsValues, dataItem) === countNum
            case '>':
              return this.countBy(fieldsValues, dataItem) > countNum
            case '>=':
              return this.countBy(fieldsValues, dataItem) >= countNum
            case '<':
              return this.countBy(fieldsValues, dataItem) < countNum
            case '<=':
              return this.countBy(fieldsValues, dataItem) <= countNum
          }
        })

        if (isDistinct) {
          fieldsValues = uniq(fieldsValues)
        }
        bus.setFieldsValue({
          [fieldKey]: fieldsValues.length + ''
        })
      })
    }

    if (this.props.KA_NUMBER_FIELD_COUNT_RULE_2) {
      if (!data || data.length === 0) return
      /**
       * 由于默认添加的费用明细不存在 ID ，后续的 ID 是由于 preparPayPlanInfosByDetailsChange 方法赋值的。
       * 当存在这个能力时，自动加上 uuid 。
       */
      data.forEach(detail => {
        detail.feeTypeForm.detailId = detail.feeTypeForm.detailId ?? uuid(14)
      })
      this.startAutoCalc2(data)
    }

    let currentEditField = null
    if (data?.currentEditField) {
      currentEditField = data.currentEditField
      delete data.currentEditField
    }
    this.setState({ errorMsg: '' })
    this.newestDetails = data
    this.valuation = {}
    data?.forEach(d => {
      if (!d.feeTypeForm || !d.feeTypeForm.ordersData) return
      const ordersData = d.feeTypeForm.ordersData && d.feeTypeForm.ordersData[0]
      const { amount } = d.feeTypeForm
      if (ordersData && ordersData.platform === 'transact') {
        const valuationItem = this.valuation[ordersData.transactRecordId]
        if (valuationItem) {
          valuationItem.used = new MoneyMath(valuationItem.used).add(amount).value
        } else {
          this.valuation[ordersData.transactRecordId] = {
            total: amount,
            used: amount
          }
        }
      }
    })
    if (!data || !data.length) {
      this.PayPlanStore?.clearObjs()
      if (this.props.customizeQueryPower) {
        //业务对象联动赋值
        this.isFirstCustomizeCalFinished = false
        currentEditField && this.updateCustomizeCalResult(undefined, currentEditField, true)
      }
      return
    }
    //等 form 赋值到表单上
    setTimeout(() => {
      this.fnGetWrittenOffResult().then((writtenOff = []) => {
        const writtenOffTotal = total(writtenOff?.map(v => getMoney(v.amount) || 0)).toString()
        this.preparPayPlanInfosByDetailsChange(data, writtenOffTotal)
        if (this.props.customizeQueryPower) {
          //业务对象联动赋值
          this.isFirstCustomizeCalFinished = false
          currentEditField && this.updateCustomizeCalResult(undefined, currentEditField, true, '', true)
        }
        // 附件字段配置单据详情发票指定类型必填逻辑
        const hasAttachmentConfig = this.getAttachmentOfInvoiceType()
        if (hasAttachmentConfig) {
          this.updateAutoCalResult(undefined, undefined, true)
        }
      })
    }, 0)
  }

  calculatePayAmountBySettlement = (amount, companyRealPay, settlement) => {
    //没有结算方式或结算是随单付
    if (!settlement || settlement?.opportunity === 'SINGLEPAYMENT') {
      let payMoney = new MoneyMath(amount)
      if (companyRealPay) {
        payMoney = payMoney.minus(companyRealPay)
      }
      return payMoney.value
    } else {
      let payMoney = new MoneyMath(amount).minus(amount)
      if (companyRealPay) {
        payMoney = payMoney.minus(companyRealPay)
      }
      return payMoney.value
    }
  }

  preparPayPlanInfosByDetailsChange = async (details = [], writtenOff = '') => {
    if (!this.PayPlanStore) return
    if (!details.length && !writtenOff) {
      this.PayPlanStore.clearObjs()
      return
    }
    if (details.length > 1) {
      const { template = [] } = this.state
      const detailField = template.find(v => v.name === 'details')
      details = sortFeeTypeForm(details, detailField?.orderByType)
    }
    const { multiplePayeesMode, payPlanMode, payeePayPlan, payPlanList = [] } = this.props
    if (multiplePayeesMode) {
      details.forEach(d => {
        const { amount, feeDetailPayeeId, companyRealPay, settlement, apportions, detailId = uuid(14) } =
          d.feeTypeForm || {}
        if (!amount) return
        const payAmount = this.calculatePayAmountBySettlement(amount, companyRealPay, settlement)
        const payPlan = payPlanList.find(p => {
          const isSameId = p.dataLinkForm.payeeId?.id === feeDetailPayeeId?.id
          const isSameStrCode = p.dataLinkForm.E_system_支付计划_支付金额?.foreignStrCode === amount.foreignStrCode
          return isSameId && isSameStrCode
        })
        if (payPlan && payeePayPlan) {
          const payPlanAmount = payPlan.dataLinkForm.E_system_支付计划_支付金额
          payPlan.dataLinkForm.E_system_支付计划_支付金额 = new MoneyMath(payPlanAmount).add(payAmount).value
        } else {
          payPlanList.push({
            apportions,
            dataLinkId: detailId,
            dataLinkForm: {
              payeeId: feeDetailPayeeId,
              E_system_支付计划_支付金额: payAmount
            }
          })
        }
      })
    }
    if (payPlanList.length && !payPlanMode) {
      this.PayPlanStore.initObjs(payPlanList)
    }
    this.calculatePayPlanMoney(details, writtenOff)
  }

  handleWrittenOffChange = (writtenOffTotalInStandard, writtenOffSummaryForMutiCurrency, writtenOffTotal) => {
    const writtenOff = writtenOffTotalInStandard || writtenOffTotal
    this.preparPayPlanInfosByDetailsChange(this.newestDetails, writtenOff)
  }

  resetPayPlanData = (details = []) => {
    details.forEach(d => {
      if (d.feeTypeForm) {
        const { amount, companyRealPay, settlement, detailId } = d.feeTypeForm
        const payAmount = this.calculatePayAmountBySettlement(amount, companyRealPay, settlement)
        const target = this.PayPlanStore.objs.find(item => item.key === detailId)
        if (target) target.money = payAmount
      }
    })
  }

  calculatePayPlanMoney = (details = [], writtenOffTotal) => {
    const { dataSource = {}, payPlanMode } = this.props
    const companyPayMoneyBig = total(details.map(v => v.feeTypeForm.companyRealPay).filter(o => o))
    const { payDetail } = getDetailCalculateMoney(details || [], dataSource.formType)
    const expenseMoneyBig = payDetail.value
    let payMoneyBig = new MoneyMath(expenseMoneyBig).minus(companyPayMoneyBig).value
    if (expenseMoneyBig && writtenOffTotal) {
      payMoneyBig = new MoneyMath(payMoneyBig).minus(writtenOffTotal).value
    }
    if (!payPlanMode && writtenOffTotal !== '0' && !!writtenOffTotal) {
      const payPlanObjs = this.PayPlanStore.writtenOffObjs
      // this.resetPayPlanData(details) //按明细 重置支付计划数据
      let temp = 0
      // 按从上到下顺序对支付计划核销
      for (let i = 0; i < payPlanObjs.length; i++) {
        const item = payPlanObjs[i]
        const standard = Number(item.money.standard)
        const standardScale = Number(item.money.standardScale || 2)
        if (i === 0) {
          temp = Number(new Big(standard).minus(Number(writtenOffTotal) || 0).toFixed(standardScale) || 0)
        } else {
          temp = Number(new Big(standard).plus(temp).toFixed(standardScale))
        }
        if (temp >= 0) {
          item.money.standard = temp
          if (item.money.hasOwnProperty('foreign') && item.money.standardNumCode) {
            item.money.foreign = new Big(item.money.standard)
              .div(item.money.rate)
              .toFixed(Number(item.money.foreignScale))
          }
          break
        } else if (temp < 0) {
          item.money.standard -= item.money.standard
          item.money.standard = temp < 0 ? 0 : temp
          if (item.money.hasOwnProperty('foreign') && item.money.standardNumCode) {
            item.money.foreign = new Big(item.money.standard)
              .div(item.money.rate)
              .toFixed(Number(item.money.foreignScale))
          }
        }
      }
    }
    const payMoneyBigValue = typeof payMoneyBig === 'string' ? payMoneyBig : payMoneyBig.standard
    const isNe = isNegat(payMoneyBigValue)
    const myValue = Math.abs(payMoneyBigValue || 0)
    this.PayPlanStore.targetTotalMoney = payMoneyBigValue
    this.PayPlanStore.isNe = isNe
    this.PayPlanStore.myValue = myValue
    this.forceUpdate()
  }

  updateVisibleFeeTypes = othersParam => {
    const { bus, dataSource, baseDataProperties } = this.props
    const { currentSpecification } = this.state
    return bus.getValue().then(value => {
      let param = parseFormValueAsParam(value, currentSpecification, dataSource, baseDataProperties)
      let params = {
        formData: param.form // 历史接口的固定参数： formData
      }
      if (othersParam) {
        params = {
          ...params,
          ...othersParam
        }
      }
      return api.invokeService('@bills:get:visibleFeetype:ByFormData', params)
    })
  }

  // 添加行程 addtrip
  fnAddTrips = ({ value, external, shouldUpdate, canEdit, currentTrips }) => {
    const {
      currentSpecification: { configs = [] },
      submitterId
    } = this.state
    let { dataSource, bus, setValidateError } = this.props
    const onlyTrips = getV(dataSource, 'form.trips', [])
    const requisitionConfig = configs.find(v => v.ability === 'requisition') || {}
    const {
      tripType: { isAll, ids = [] }
    } = requisitionConfig
    const trips = canEdit ? value : [currentTrips]
    return api.invokeService('@bills:get:getTripsTemplate').then(action => {
      let templates = action.items
      if (!isAll) {
        templates = templates.filter(v => !!~ids.indexOf(v.id))
      }
      if (templates.length) {
        return bus.getValue().then(data => {
          return api.open('@bills:AddTripsModal', {
            templates,
            trips,
            onlyTrips,
            submitterId,
            shouldUpdate,
            external,
            flowId: dataSource.id,
            billSpecification: this.state.currentSpecification,
            billData: data,
            setValidateError,
            canEdit
          })
        })
      } else {
        showMessage.error(i18n.get('没有可用的费用类型,请前往「行程类型」配置'))
      }
    })
  }

  //委托人发生变更时的事件
  fnSetDelegator = submitter => {
    const { AutoGenerateFeeDetail } = this.props
    if (!this.state.currentSpecification.id) {
      return
    }
    this.setState({ submitterId: submitter }, () => {
      this.fnGetApplyByExpense(this.state.currentSpecification)
      this.fnGetCalculateField(submitter, true, false)
      this.reloadStaffAndDeptment(submitter.id)
    })
    //触发自动生成费用明细的提交人字段
    if (AutoGenerateFeeDetail) {
      this.fnAutoGenerationFee(null, 'submitterId', this.state.currentSpecification)
    }
  }

  updateCalTemplate = value => {
    const { bus, setValidateError } = this.props
    const { template, formRiskWarningData } = this.state
    const clearValidFields = []
    const fields = Object.keys(value)
    fields.forEach(field => {
      let index = template.findIndex(el => el.field === field)
      if (index !== -1) {
        template[index] = { ...template[index], ...value[field] }
        if (value[field]['optional']) {
          clearValidFields.push(field)
        }
      }
    })

    this.setState({ template: [...template] }, () => {
      setValidateError({ bill: [], detail: [], trip: [] })
      if (clearValidFields && clearValidFields.length) {
        bus.getValueWithValidate(clearValidFields)
        formRiskWarningData && bus?.setFieldsExternalsData?.({ ...formRiskWarningData })
      }
    })
  }

  updateApplyFromExpense = params => {
    if (this.state.currentSpecification) {
      this.fnGetApplyByExpense({ ...this.state.currentSpecification, ...(params || {}) })
    }
  }

  reloadStaffAndDeptment = id => {
    const { isFilterStaffAndDept } = this.props
    //配置了钉钉人员可见性时，切换委托人获取委托人的可见性数据,如果没有配置钉钉可见性则所有人看到的数据时相同的
    if (isFilterStaffAndDept) {
      api.invokeService('@common:get:visibility:staffs', id)
      api.invokeService('@common:get:visibility:department:tree', id)
    }
  }

  fnGetApplyByExpense(currentSpecification, expenseLink, submitterId = this.state.submitterId) {
    let { getApplyByExpense, getApplyByExpenseWithOrder, dimentionCurrencyInfo } = this.props
    let applyAbility = currentSpecification.configs.find(v => v.ability === 'apply')
    if (applyAbility && applyAbility.canApply) {
      const legalEntityMultiCurrency = get(dimentionCurrencyInfo, 'dimention.id')
      //获取关联模版
      // getApplyByExpense({
      //   id: currentSpecification.id,
      //   submitterId: submitterId.id,
      //   expenseLink,
      //   legalEntityMultiCurrency
      // })
      getApplyByExpenseWithOrder({
        id: currentSpecification.id,
        submitterId: submitterId.id,
        expenseLink,
        legalEntityMultiCurrency,
        query: currentSpecification.query
      })
    }
  }

  handleGetBillsValue = () => {
    let { bus, type } = this.props
    let { template } = this.state
    if (type === 'requisition') {
      return this.handleGetTripsBillsValue(bus, template)
    }
    return bus.getValue().then(values => {
      let detailsMapPath = {
        details: 'values.details',
        components: 'specificationId.components',
        fieldsValue: 'feeTypeForm'
      }
      return { values, template, detailsMapPath }
    })
  }

  handleGetTripsBillsValue = (bus, template) => {
    return bus.getValue().then(values => {
      let cloneValues = cloneDeep(values)
      let detailsMapPath = {
        details: 'values.trips',
        components: 'tripTypeId.specification.components',
        fieldsValue: 'tripForm'
      }
      return api.invoke('get:trips:details:value').then(
        result => {
          cloneValues.trips = result
          return { values: cloneValues, template, detailsMapPath }
        },
        _ => {
          return { values, template, detailsMapPath }
        }
      )
    })
  }

  fnDetailBatchApportion = async (apportionSpecificationIds, selectValue) => {
    const billData = await this.props.bus.getValue()
    return api.open('@apportion:BatchApportionModal', { apportionSpecificationIds, selectValue, billData: billData })
  }

  //新加后两个参数，区分：委托人变更时， 草稿 驳回态打开时，切换模板时
  fnGetCalculateField = (submitter, isDelegatorChange, onlyQueryFields, isAutoAssignOfOneResultOfDataLink = false) => {
    if (isAutoAssignOfOneResultOfDataLink) {
      this.isInitAutoAssignOfOneResultOfDataLink = true
    }
    const { currentSpecification } = this.state
    const { getCalculateField, customizeQueryPower } = this.props
    getCalculateField(currentSpecification.id, submitter.id).then(action => {
      if (action.error) {
        return
      }
      let autoRules = action.payload.items
      let autoCalFields = autoRules[0]
      this.isFirstAutoCalFinished = false
      this.updateAutoCalResult(undefined, undefined, true, isAutoAssignOfOneResultOfDataLink)
      this.setState({ autoCalFields })
    })
    if (customizeQueryPower) {
      this.isFirstCustomizeCalFinished = false
      //客户单据偶现第一次联查bug.getValue时没有提交人，无法复现 先加下延迟看客户反馈
      setTimeout(() => {
        this.updateCustomizeCalResult(
          undefined,
          isDelegatorChange ? [{ type: 'master_', values: ['submitterId'], operate: 'editBill' }] : undefined,
          true,
          onlyQueryFields
        )
      }, 500)
    }
  }

  handleImportDetailByExcel = async (visibleFeeTypes, data, options) => {
    const { submitterId, currentSpecification } = this.state
    const {
      multiplePayeesMode,
      payPlanMode,
      bus,
      dataSource,
      baseDataProperties,
      lastChoice,
      baseDataPropertiesMap,
      dimentionCurrencyInfo
    } = this.props
    const billData = await bus.getValue()
    return api
      .open('@bills:ImportDetailByExcel', {
        type: 'detail',
        flag: {
          visibleFeeTypes,
          billSpecification: currentSpecification,
          submitterId,
          multiplePayeesMode,
          payPlanMode,
          billData: parseFormValueAsParam(billData, currentSpecification, dataSource, baseDataProperties),
          dimention: dimentionCurrencyInfo?.dimention,
          isCancelLimit: options?.isCancelLimit
        }
      })
      .then(result => {
        const defaultValueMap = {}
        const specificationMap = {}
        const details = result.map((v, idx) => {
          if (typeof v.feeTypeId === 'string') {
            let feeType = getFeeTypeById(data, v.feeTypeId)
            v.feeTypeId = feeType
          }
          if (typeof v.specificationId === 'string') {
            v.specificationId = JSON.parse(v.specification)
            delete v.specification
          }
          specificationMap[v.specificationId.id] = v.specificationId
          v.idx = idx
          return v
        })
        const promiseList = Object.keys(specificationMap).map(id => {
          defaultValueMap[id] = {}
          const specification = specificationMap[id]
          const defaultValueList = specification.components.map(c => {
            const field = baseDataPropertiesMap[c.field]
            return getDefaultValue(c, field, lastChoice, submitterId).then(defaultValue => {
              if (defaultValue) {
                defaultValueMap[id][c.field] = defaultValue
              }
              return
            })
          })
          return Promise.all(defaultValueList)
        })
        return Promise.all(promiseList).then(_ => {
          return details.map(v => {
            v.feeTypeForm = { ...defaultValueMap[v.specificationId.id], ...v.feeTypeForm }
            return v
          })
        })
      })
  }

  //自动生成费用明细
  fnAutoGenerationFee = async (data, changeValues, currentSpecification) => {
    const { bus } = this.props
    !data && bus && (data = await bus.getValue())
    const { generationField, generationRule } = this.state
    if (generationField?.length && (changeValues === 'all' || generationField.includes(changeValues)) && data) {
      //当前字段存在在自动生成费用明细的监听字段里
      let formDetails = cloneDeep(data?.details) || []
      const billData = {
        form: { ...data, specificationId: currentSpecification?.id },
        billType: currentSpecification?.type,
        currentEditField: data?.details && changeValues && changeValues !== 'all' ? changeValues : '',
        startIdx: formDetails.length
      }
      //拿到这个字段对应的自动生成费用明细
      api.dispatch(getAutoGenerationFeeDetail(billData)).then(async res => {
        let autoDetails = res?.items
        let subDetails = []
        if (autoDetails && autoDetails.length > 0) {
          //如果没有formDetails,直接插入生成的费用明细
          let cur = await bus.getValue()
          formDetails = cloneDeep(cur?.details)
          if (!formDetails || (formDetails && formDetails.length === 0)) {
            autoDetails = this.ResetAutoGenerationFeeIdx(autoDetails)
            bus.emit('dynamic:detail:change', autoDetails)
          } else {
            //如果表单已经存在明细
            let detailsArr = []
            autoDetails.forEach(i => {
              let id = i?.feeTypeId?.id
              if (detailsArr.indexOf(id) < 0) {
                detailsArr.push(id)
              }
            })
            formDetails = formDetails.filter(v => {
              return !v?.feeTypeForm?.isAutoDetail || detailsArr.indexOf(v?.feeTypeId?.id) < 0
            })
            subDetails = this.ResetAutoGenerationFeeIdx(formDetails.concat(autoDetails))
            bus.emit('dynamic:detail:change', subDetails)
          }
        } else {
          let cur = await bus.getValue()
          formDetails = cloneDeep(cur)?.details
          generationRule.forEach(i => {
            if (i.field == changeValues) {
              formDetails = formDetails.filter(v => {
                return !v?.feeTypeForm?.isAutoDetail || v.feeTypeId.id != i.feeTypeId
              })
              subDetails = this.ResetAutoGenerationFeeIdx(formDetails.concat(autoDetails))
              bus.emit('dynamic:detail:change', subDetails)
            }
          })
        }
      })
    }
  }

  ResetAutoGenerationFeeIdx(details) {
    details?.length &&
      details.forEach((v, index) => {
        v.idx = index
      })
    return details
  }

  handleHABValueChange = value => {
    const { bus } = this.props
    bus.setFieldsValue(value)
  }
  handleDynamicValueChange = changeValues => {
    const { bus } = this.props
    const { template, currentSpecification, dependenceFieldOfDataLink, timeField } = this.state

    let changeKeys = Object.keys(changeValues)
    if (timeField && changeKeys.includes(timeField)) {
      this.handleChangeTimeField({})
    }

    if (!this.__CURRENT_IS_CHANGED) {
      this.isFormValueChange(changeValues)
    }
    this.props?.bus?.getValue().then(data => {
      if (currentSpecification.type === 'requisition') {
        const pay = currentSpecification.configs.find(v => v.ability === 'pay')
        const money = Number(data?.loanMoney?.standard ?? 0)
        if (pay && pay?.optionalPayeeByZero && this.loanMoney !== money) {
          this.loanMoney = money
          this.forceUpdate()
        }
      }
    })
    bus?.has('bill:value:changed:forhab') && bus?.emit('bill:value:changed:forhab', changeValues)
    bus?.has('bill:value:changed:forAIAttachment') && bus?.emit('bill:value:changed:forAIAttachment', changeValues)
    /**
     *  @description: 当前模板为 Text 时，更新模板值不走动态值变更逻辑
     *  @description: 因为输入框change事件多次触发自动计算，改为只有在失去焦点的时候才会触发自动计算
     */
    const currentField = Object.keys(changeValues)[0]

    if (!currentField) {
      return
    }

    const currentTemplate = template.find(t => t.name === currentField)

    if (!currentTemplate) {
      return
    }

    this.updateCalculateField(changeValues)

    setTimeout(() => {
      this.updateCustomizeCalculate(changeValues)
      // 判断变更字段是否属于业务对象依赖字段,如果是执行业务对象唯一值自动赋值
      const currentDependenceField = getDependenceFieldOfDataLink(dependenceFieldOfDataLink, changeValues)
      if (currentDependenceField && !this.isInitAutoAssignOfOneResultOfDataLink) {
        this.initAutoAssignOfOneResultOfDataLink(currentDependenceField?.fields)
      }
      //自动生成费用明细
      this.fnAutoGenerationFee(null, Object.keys(changeValues)[0], currentSpecification)
    }, 100)
  }

  handleDynamicValueBlur = changeValues => {
    const { bus } = this.props
    this.updateCalculateField(changeValues)

    setTimeout(() => {
      this.updateCustomizeCalculate(changeValues)
    }, 100)
    bus?.has('bill:value:changed:forhab') && bus.emit('bill:value:changed:forhab', changeValues)
  }

  updateCalculateField = (changeValues, isTextChange) => {
    const { autoCalFields } = this.state
    const fn = () => {
      Object.keys(changeValues).forEach(key => {
        if (needUpdateResult(key, autoCalFields)) {
          this.updateAutoCalResult(changeValues, isTextChange)
        }
      })
    }
    if (getBoolVariation('cyxq-75092')) {
      callFnByQueueNew({}, fn)
    } else {
      callFnByQueue(fn)
    }
  }

  updateCustomizeCalculate = changeValues => {
    const { customizeQueryRule } = this.state
    const fn = () => {
      Object.keys(changeValues).forEach(key => {
        if (needUpdateCustomizeResult(key, customizeQueryRule)) {
          this.updateCustomizeCalResult(changeValues)
        }
      })
    }
    callCustomizeCalByQueue(fn)
  }

  isFirstAutoCalFinished = false
  isFirstCustomizeCalFinished = false

  updateAutoCalResult = debounce(
    async (changeValues = {}, isTextChange, checkDefaultValue, isAutoAssignOfOneResultOfDataLink = false) => {
      const { bus, baseDataProperties, baseDataPropertiesMap, dataSource, canUseDefaultCurrency } = this.props
      const { currentSpecification, submitterId, template } = this.state
      let formValue = await bus.getValue() //  当前表单值
      if (!formValue) {
        return false
      }
      formValue.specificationId = currentSpecification?.id
      if (dataSource.id) {
        formValue.flowId = dataSource.id
      }
      formValue = { ...formValue, ...changeValues }
      const code = getV(dataSource, 'form.code')
      if (code) {
        formValue.code = code
      }
      const needUpdateDefaultValue = !this.isFirstAutoCalFinished && dataSource.state === 'new'
      await updateAutoCalResult(
        'master_',
        formValue,
        formValue,
        currentSpecification,
        baseDataProperties,
        baseDataPropertiesMap,
        bus,
        submitterId.id,
        changeValues,
        isTextChange,
        needUpdateDefaultValue,
        template
      )
      if (isAutoAssignOfOneResultOfDataLink) {
        this.initAutoAssignOfOneResultOfDataLink()
      }
      if (!this.isFirstAutoCalFinished) {
        this.isFirstAutoCalFinished = true
        canUseDefaultCurrency && dataSource.state === 'new' && setDefaultCurrencyMoney.call(this)
      }
    },
    400
  )

  updateCustomizeCalResult = debounce(
    async (changeValues = {}, changedFields, checkDefaultValue, onlyQueryFields, filterDataLinkFields) => {
      const { bus, baseDataProperties, baseDataPropertiesMap, dataSource } = this.props
      const { currentSpecification, submitterId, template } = this.state
      let formValue = (await bus.getValue()) || {} //  当前表单值
      formValue.specificationId = currentSpecification?.id
      if (dataSource.id) {
        formValue.flowId = dataSource.id
      }
      formValue = { ...formValue, ...changeValues }
      const code = getV(dataSource, 'form.code')
      if (code) {
        formValue.code = code
      }
      formValue.details = this.newestDetails
      const needUpdateDefaultValue = checkDefaultValue && dataSource.state === 'new'
      const calculateSpecificationID = currentSpecification?.id
      const customizeQueryRule = await updateCustomizeCalResult(
        'master_',
        formValue,
        formValue,
        currentSpecification,
        baseDataProperties,
        baseDataPropertiesMap,
        bus,
        submitterId.id,
        changeValues,
        changedFields,
        needUpdateDefaultValue,
        template,
        calculateSpecificationID,
        dataSource.state,
        onlyQueryFields,
        filterDataLinkFields
      )
      customizeQueryRule && this.setState({ customizeQueryRule: customizeQueryRule?.queryFields })
      if (checkDefaultValue) {
        this.isFirstCustomizeCalFinished = true
      }
    },
    100
  )

  generateDetailByAutoCalculate = async (
    { detailFormValue, components, isMeta, needCalculateRecordLink = true },
    isFormValue = false
  ) => {
    const { bus, dataSource, baseDataProperties, lastChoice, baseDataPropertiesMap } = this.props
    const { currentSpecification, submitterId } = this.state
    const defaultValueMap = {}
    const promiseList = components.map(c => {
      const field = baseDataPropertiesMap[c.field]
      return getDefaultValue(c, field, lastChoice, submitterId).then(data => {
        defaultValueMap[c.field] = data
      })
    })
    if (isMeta) {
      const feeTypeForm = await getObjById(detailFormValue.feeTypeForm, components)
      detailFormValue.feeTypeForm = { ...detailFormValue.feeTypeForm, ...feeTypeForm }
    }

    const returnValue = form => {
      return isFormValue ? form : form.feeTypeForm
    }
    return new Promise(resolve => {
      Promise.all(promiseList).then(_ => {
        detailFormValue.feeTypeForm = { ...defaultValueMap, ...detailFormValue.feeTypeForm }
        bus.getValue().then(billData => {
          billData.specificationId = currentSpecification.id
          if (dataSource.id) {
            billData.flowId = dataSource.id
          }
          billData.details = [detailFormValue]
          const params = getParam(
            'detail_',
            billData,
            detailFormValue,
            currentSpecification,
            baseDataProperties,
            undefined,
            true
          )

          const updateResult = res => {
            // 自动计算数据
            const action = res[0]

            // 档案数据
            let obj = {}
            if (res?.length >= 1) {
              obj = res[1]
            }

            if (action.error) {
              detailFormValue.feeTypeForm = { ...detailFormValue.feeTypeForm, ...obj }
              return resolve(returnValue(detailFormValue))
            }
            const r = {}
            const results = action.items
            results &&
              results.forEach(element => {
                const { onField, result, resultType, attribute, numCode, currencyType, amount } = element
                const _result = result === '' || result === 'null' ? undefined : result // 为了匹配老的自动计算接口奇奇怪怪的返回值
                let component = components.find(cur => cur.field === onField) || {}
                let specificationComponent = detailFormValue?.specificationId?.components?.find?.(
                  cur => cur.field === onField
                )
                const detailComponents = get(detailFormValue, 'specificationId.components', [])
                if (resultType === 'VALUE') {
                  let { type, entity } = get(baseDataPropertiesMap, `${onField}.dataType`)
                  if (!entity && type === 'list') {
                    entity = get(baseDataPropertiesMap[onField], 'dataType.elemType.entity')
                  }
                  let value
                  if (component.editable) {
                    value = result || detailFormValue.feeTypeForm[onField] || undefined
                  } else {
                    value = result || undefined
                  }

                  // 配置了按币种自定义计算规则的字段，需要参数resultCurrencyInfo：
                  const resultCurrencyInfo = get(component, 'defaultValue.customRuleId')
                    ? { currencyType, numCode, amount }
                    : null

                  r[onField] = formatValue(component.type, entity, value, component, undefined, resultCurrencyInfo)
                } else if (resultType === 'SPECIFICATION_ATTRIBUTE') {
                  const isApparitionsOpenChange =
                    attribute === 'open' ? String(component[attribute]) !== _result : false
                  const isAttrEquals = checkAttrChange(
                    component[attribute],
                    _result,
                    component,
                    baseDataPropertiesMap[onField]
                  )
                  if (!isAttrEquals || isApparitionsOpenChange) {
                    const attrObj = formatAttrValue(
                      attribute,
                      _result,
                      detailComponents,
                      baseDataPropertiesMap[onField]
                    )
                    component = { ...component, ...attrObj }
                    switch (attribute) {
                      case 'open': {
                        if (!specificationComponent) {
                          break
                        }
                        specificationComponent.open = _result === 'true'
                        break
                      }
                    }
                  }
                  const type =
                    get(baseDataPropertiesMap[onField], 'dataType.elemType.type') ||
                    get(baseDataPropertiesMap[onField], 'dataType.type')
                  if (attribute === 'optional') {
                    let isOptional = false
                    if (type === 'attachment') {
                      isOptional =
                        component[attribute] === false &&
                        (!detailFormValue.feeTypeForm?.[onField] ||
                          !(
                            Array.isArray(detailFormValue.feeTypeForm?.[onField]) &&
                            detailFormValue.feeTypeForm?.[onField].length
                          ))
                    } else {
                      const onFieldValue = detailFormValue.feeTypeForm?.[onField] || r[onField]
                      if (onField === 'invoiceForm' && fnInvoiceValid(onFieldValue)) {
                        isOptional = _result === 'true'
                      } else {
                        isOptional = component?.[attribute] == false && !onFieldValue
                      }
                    }
                    if (isOptional) {
                      detailFormValue.errorMsg = {
                        completed: i18n.get('消费明细填写不完整；'),
                        isCheckCalAttr: true
                      }
                    }
                  } else if (attribute === 'open') {
                    if (_result === 'true') {
                      detailFormValue.errorMsg = {
                        completed: i18n.get('消费明细填写不完整；'),
                        isCheckCalAttr: true
                      }
                    }
                  }
                }
              })
            const linkageAssignmentField = components.find(
              element =>
                element.type === 'dataLink' &&
                element.defaultValue &&
                element.defaultValue.type === 'formula' &&
                element.isLinkageAssignment
            )
            if (linkageAssignmentField && r[linkageAssignmentField.field]) {
              let id = r[linkageAssignmentField.field]
              setDataLinkFormValue(typeof id == 'string' ? id : id.id, components, linkageAssignmentField).then(
                dataLinkFormValue => {
                  detailFormValue.feeTypeForm = { ...detailFormValue.feeTypeForm, ...dataLinkFormValue, ...r, ...obj }
                  resolve(returnValue(detailFormValue))
                }
              )
            } else {
              detailFormValue.feeTypeForm = { ...detailFormValue.feeTypeForm, ...r, ...obj }
              resolve(returnValue(detailFormValue))
            }
          }

          if (needCalculateRecordLink) {
            return Promise.all([
              api.invokeService('@bills:get:calculationresult', params),
              this.generateCalculateRecordLink(components, params.billData)
            ])
              .then(res => {
                updateResult(res)
              })
              .catch(() => {
                return resolve(returnValue(detailFormValue))
              })
          }
          return api.invokeService('@bills:get:calculationresult', params).then(res => {
            updateResult([res])
          })
        })
      })
    })
  }

  /** 私车公用档案关系字段查询接口 */
  generateCalculateRecordLink = async (components, billData) => {
    return new Promise(resolve => {
      // 如果存在档案关系的字段，则取档案关系赋值
      if (components.find(cur => cur.dependence && cur.dependence.length)) {
        api
          .invokeService('@bills:get:calculateRecordLink', { form: billData })
          .then(action => {
            resolve(action.items?.[0]?.feeTypeForm || {})
          })
          .catch(() => {
            resolve({})
          })
      } else {
        resolve({})
      }
    })
  }

  checkValueCallback = resolve => {
    related.clearRelatedData()
    api.invokeService('@bills:update:dimention:currency', null)
    return resolve()
  }

  isFormValueChange = data => {
    let { bus, baseDataProperties } = this.props
    const { currentSpecification } = this.state
    let valueToCompare = {}
    Object.keys(data).forEach(k => {
      valueToCompare[k] = this.__CURRENT_DYNAMIC_VALUE[k]
    })
    let value1 = fnParseFormValue(valueToCompare, currentSpecification, baseDataProperties)
    let value2 = fnParseFormValue(data, currentSpecification, baseDataProperties)
    let bl = isEqual(value1, value2)
    //不一致, 说明表单值已被修改
    this.__CURRENT_IS_CHANGED = !bl
    bus.__CURRENT_IS_CHANGED = this.__CURRENT_IS_CHANGED
  }

  handleCheckValueChange = () => {
    if (!this.checkPromise) {
      this.checkPromise = new Promise((resolve, reject) => {
        setTimeout(() => {
          const { bus } = this.props
          if (this.__CURRENT_IS_CHANGED) {
            return exitConfirm(bus).then(
              res => {
                if (res === 'save') {
                  if (supportBillDetailsSwitchingInDrawer()) {
                    bus
                      .getValueWithValidate(1)
                      .then((formValue) => {
                        return this.handleSaveBill(formValue, {
                          updateSelectLine: false,
                        })
                      }).then(() => {
                        bus.reload?.()
                        resolve()
                      })
                  } else {
                    bus.invoke('footer:action:save', {
                      updateSelectLine: false, //updateSelectLine false表示不更新单据的选中列
                      checkValueCallback: () => this.checkValueCallback(resolve)
                    })
                  }
                } else {
                  this.checkValueCallback(resolve)
                }
              },
              error => {
                return reject('cancel')
              }
            )
          } else {
            related.clearRelatedData()
          }
          api.invokeService('@bills:update:dimention:currency', null)
          return resolve()
        }, 0)
      }).then(
        data => {
          this.checkPromise = undefined
          return data
        },
        error => {
          this.checkPromise = undefined
          return Promise.reject(error)
        }
      )
    }
    return this.checkPromise
  }

  fnSelectStaff = async data => {
    const isByRule = await this.fnGetBillDate(data.staffRangeRule)
    const { dataSource, userInfo, externalStaffPower } = this.props
    const { field, ...others } = data
    const params = { ...others }
    if (dataSource?.openFrom === 'flow-preview' && field?.name === 'submitterId') {
      delete params.dataSource
    }
    const types = []
    if (field?.allowInteriorStaff) {
      //允许选择内部员工
      types.push('department-member')
    }
    let allowExternalStaff = field?.allowExternalStaff
    if (allowExternalStaff) {
      // 允许选择外部员工
      types.push('external')
    }
    if (!allowExternalStaff && field?.name === 'submitterId') {
      let hasExternalStaff = false
      if (externalStaffPower && dataSource?.openFrom === 'flow-preview') {
        // 审批流预览时开启了外部人员登录charge，就可以预览所有的外部人员提交流程
        hasExternalStaff = true
      } else {
        const ownerId = get(userInfo, 'staff.id')
        // 允许选择外部员工
        hasExternalStaff = data?.dataSource?.some(staff => staff?.external && staff?.id !== ownerId)
      }
      if (hasExternalStaff) {
        allowExternalStaff = true // 外部人员charge关闭了，但是委托配置的还有外部人员，则允许选择外部人员
        if (types.length === 0) {
          types.push('department-member')
        }
        types.push('external')
      }
    }
    return billSelectStaff({ ...params, isByRule, allowExternalStaff }, data.multiple, types)
  }

  fnSelectStaffs = async data => {
    const isByRule = await this.fnGetBillDate(data.staffRangeRule)
    return billSelectStaff({ ...data, isByRule }, true, [])
  }

  fnGetBillDate = async ruleId => {
    const result = ruleId && ruleId !== 'false'
    if (!result) return result
    const { bus, dataSource, baseDataProperties } = this.props
    const { currentSpecification } = this.state
    const billData = await bus.getValue().then(formData => {
      formData.specificationId = currentSpecification.id
      if (dataSource.id) {
        formData.flowId = dataSource.id
      }
      const params = getParam('detail_', formData, formData, currentSpecification, baseDataProperties, undefined, true)
      return params.billData
    })
    return api.dataLoader('@common.staffRangeByRule').reload({ ...billData, ruleId })
  }

  fnSelectPayee = (selectedPayee, dependenceList, _, options) => {
    const { isModify, dataSource } = this.props
    const { currentSpecification } = this.state
    const templateid = currentSpecification.id
    let modalWays = '@bills:SelectPayeeModal'
    if (newVersionOPaymenAccount()) {
      modalWays = '@bills:SelectPayeePopup'
    }
    return api.open(modalWays, {
      isModify,
      flowId: isModify ? dataSource.id : void 0,
      data: selectedPayee,
      templateid,
      dependenceList,
      billSpecification: currentSpecification,
      allowCancelDependence: options?.allowCancelDependence,
      filterRules: options?.filterRules
    })
  }

  handleSelectDataLink = entityInfo => {
    let { bus, dataSource, baseDataProperties } = this.props
    let { currentSpecification, submitterId } = this.state
    return bus.getValue().then(data => {
      let value = parseFormValueAsParam(data, currentSpecification, dataSource, baseDataProperties)
      return this.fnGetWrittenOffResult().then(writtenOff => {
        if (writtenOff && writtenOff.length) {
          value.params = { loanWrittenOff: writtenOff }
        }
        return api.open('@bills:SelectDataLinkModal', {
          submitterId,
          entityInfo,
          flowId: dataSource.id,
          isDetail: false,
          formValue: value
        })
      })
    })
  }

  // 打开消费详情的弹窗
  fnDetailsLineClick = ({ details, line, visibleFeeTypes, ruleLog, isEdit = true, external, index, ...othres }) => {
    let { currentSpecification, submitterId, applicationListDetails, template, timeField } = this.state
    let { bus, currentNode, isModify, dataSource, singleRiskData = {}, remunerationBatchField } = this.props
    const { riskData, riskInfo } = this.state
    const { state: billState } = dataSource
    const onlyDetails = getV(dataSource, 'form.details', [])
    const modifyApproveMoney = canModifyApproveMoney(currentSpecification)
    const isRemuneration = checkIsRemuneration(currentSpecification)
    if (isRemuneration) {
      const batchId = get(dataSource, `form.${remunerationBatchField}.id`)
      if (!batchId) {
        api.invokeService('@remuneration:first:save:bill', { bus })
        return details
      }
      return api.open('@remuneration:RemunerationDetailPopup', {
        bus,
        dataSource,
        remunerationBatchField,
        title: i18n.get('编辑酬金明细')
      })
    }
    return bus.getValue().then(data => {
      return api.open('@bills:FeeDetailViewPopup', {
        title: i18n.get('编辑消费详情'),
        dataSource: line,
        originalValue: dataSource,
        dataFromOrder: dataSource?.dataFromOrder,
        isEdit,
        billState,
        billTemplate: template,
        billSpecification: currentSpecification,
        visibleFeeTypes,
        ruleLog: ruleLog,
        submitterId,
        details,
        onlyDetails,
        currentNode,
        isModify,
        index,
        flowId: dataSource.id,
        shouldUpdate: this.getValue(line.feeTypeForm),
        external,
        isPermitForm: dataSource?.openFrom === 'permit-form',
        isForbid: riskData.isForbid && fnIsRiskError(external),
        billData: data,
        cancelCB: () => this.handleCloseDetail(true),
        onDisableInvoice: this.handleInvoiceDisable,
        removeLinkDetailEntityId: false,
        applicationListDetails,
        modifyApproveMoney,
        maskClosable: false,
        valuation: this.valuation,
        ...othres,
        riskInfo,
        riskData: { ...riskData, ...singleRiskData },
        notShowModalIfAllInvoiceSuccess: true,
        timeField
      })
    })
  }

  handleCloseDetail = isEdidtFeeType => {
    const tempConsumId = related.tempConsumId
    related.removeByConsumeId(tempConsumId)
    if (isEdidtFeeType) {
      const value = related.relatedItemMap[tempConsumId]
      const list = this.getList(value)
      const itemList = list.map(v => {
        return { consumeAmount: v.modifyValue, relateId: v.id }
      })
      related.setRelatedMap({ id: tempConsumId, value: itemList })
    }
  }

  getList = value => {
    let list = []
    value &&
      value.forEach(v => {
        list = list.concat(v.dataList)
      })
    return list
  }

  getValue = feeTypeForm => {
    //判断这个消费记录是否取过值了
    let id = feeTypeForm ? feeTypeForm.detailId : ''
    return id ? this.editedDetailList.indexOf(id) < 0 : false
  }

  fnDetailsAddClick = ({ line, details, visibleFeeTypes, onDetailsChange, update, ...others }) => {
    let { currentSpecification, submitterId, applicationListDetails, template, timeField } = this.state
    let { bus, currentNode, isModify, dataSource } = this.props
    const onlyDetails = getV(dataSource, 'form.details', [])
    const modifyApproveMoney = canModifyApproveMoney(currentSpecification)
    return bus.getValue().then(data => {
      // 从成本归属单进入时，判断是否传入了订单的企业已付金额，有值时，填入amount
      const { companyRealPayFromOrder } = dataSource?.dataFromOrder || {}
      const detailInitValue = companyRealPayFromOrder ? { amount: companyRealPayFromOrder } : undefined
      return api.open('@bills:FeeDetailViewPopup', {
        title: i18n.get('添加明细'),
        dataSource: line,
        detailInitValue,
        dataFromOrder: dataSource?.dataFromOrder,
        isEdit: true,
        visibleFeeTypes,
        originalValue: isModify ? dataSource : undefined,
        details,
        onlyDetails,
        billData: data,
        flowId: dataSource.id,
        billTemplate: template,
        billState: dataSource.state,
        shouldUpdate: update,
        submitterId,
        billSpecification: currentSpecification,
        currentNode,
        isModify,
        isPermitForm: dataSource?.openFrom === 'permit-form',
        onDetailsChange,
        cancelCB: () => this.handleCloseDetail(),
        removeLinkDetailEntityId: true,
        applicationListDetails,
        modifyApproveMoney,
        maskClosable: false,
        valuation: this.valuation,
        ...others,
        notShowModalIfAllInvoiceSuccess: true,
        timeField
      })
    })
  }

  fnDetailsInputImportClick = ({ visibilityFeeTypes, isBill, invoices }) => {
    return inputInvoiceImport.apply(this, [
      {
        visibilityFeeTypes,
        source: !!isBill ? 'importBill' : 'import',
        invoices,
        isBillInvoice: !!isBill
      }
    ])
  }

  fnDetailsImportRequistitionClick = (expenseLink, submitterId) => {
    let { currentVisibleFeeTypes } = this.props
    return api.open('@bills:ImportRequistitionModal', {
      expenseLink,
      visibleFeeTypes: currentVisibleFeeTypes,
      submitterId,
      isCopyDetail: true
    })
  }

  fnDetailsImportClick = ({ visibilityFeeTypes, isBill, invoices }) => {
    let { bus, dataSource = {} } = this.props
    let { id } = dataSource
    let { currentSpecification, submitterId } = this.state
    return bus.getValue().then(value => {
      let { details = [] } = value //用来判定是否重复导入
      return api
        .open('@bills:ImportInvoiceModal', { details, flowId: id, multiple: true, source: 'import' })
        .then(data => {
          return api.open('@bills:ImportInvoiceDetailModal', {
            invoiceList: data.invoiceList || [], // 上传PDF入口
            visibilityFeeTypes,
            billSpecification: currentSpecification,
            source: !!isBill ? 'importBill' : 'import',
            isBillInvoice: !!isBill,
            invoices,
            submitterId,
            flowId: id,
            bus,
            importType: 'pdf',
            billData: value
          })
        })
    })
  }

  fnDetailsAliPayImportClick = ({ feeTypes, isBill, invoiceList }) => {
    let { bus, dataSource = {} } = this.props
    let { id } = dataSource
    let { currentSpecification, submitterId } = this.state
    return bus.getValue().then(value => {
      return api.open('@bills:AliPayInvoiceListModal', { submitterId }).then(data => {
        return api.open('@bills:ImportInvoiceDetailModal', {
          invoiceList: data || [],
          visibilityFeeTypes: feeTypes,
          billSpecification: currentSpecification,
          source: !!isBill ? 'importBill' : 'import',
          isBillInvoice: !!isBill,
          submitterId,
          flowId: id,
          bus,
          invoices: invoiceList,
          importType: 'alipay',
          billData: value
        })
      })
    })
  }

  fnDetailsAifapiaoImportClick = ({ feeTypes, data, isBill, invoiceList }) => {
    let { bus, dataSource = {} } = this.props
    let { id } = dataSource
    let { currentSpecification, submitterId } = this.state
    return bus.getValue().then(value => {
      return api.open('@bills:ImportInvoiceDetailModal', {
        invoiceList: data || [],
        visibilityFeeTypes: feeTypes,
        billSpecification: currentSpecification,
        isBillInvoice: !!isBill,
        source: !!isBill ? 'importBill' : 'import',
        submitterId,
        flowId: id,
        invoices: invoiceList,
        bus,
        importType: 'aifapiao',
        billData: value
      })
    })
  }

  fnDetailsImportOCRClick = ({ visibilityFeeTypes, invoices, isMedical, isOverseas, isBill }) => {
    const { bus } = this.props
    const { submitterId, currentSpecification } = this.state
    const invoiceComponent = currentSpecification?.components?.find(item => item.field === 'invoiceForm')
    return bus.getValue().then(value => {
      return api.open('@bills:ImportUploadOCRModal', { isMedical, isOverseas }).then(attachmentList => {
        return api.open('@bills:ImportInvoiceDetailModal', {
          visibilityFeeTypes,
          invoiceList: attachmentList,
          attachmentList,
          submitterId,
          isMedical,
          isBillInvoice: !!isBill,
          source: !!isBill ? 'importBill' : 'import',
          isOcr: true, // 智能识别入口
          importType: isOverseas ? 'overseasInvoice' : 'ocr',
          billData: value,
          invoices,
          bus,
          importMode: invoiceComponent?.importMode,
          billSpecification: currentSpecification
        })
      })
    })
  }

  fnDetailsRecordExpendsClick = visibilityFeeTypes => {
    const { dataSource } = this.props
    const { submitterId } = this.state
    const template =
      dataSource.currentSpecification ||
      dataSource?.form?.specificationId ||
      dataSource?.requisitionInfo?.defaultSpecification
    const isEBussCard = template?.configs?.find(item => item.isEBussCard)?.isEBussCard
    return api.open('@record-expends:ImportWithnotesToBill', {
      visibilityFeeTypes,
      submitterId: submitterId,
      billType: dataSource && dataSource.formType,
      isEBussCard,
      specificationId: template?.id,
      fromSourceType: 'createBill'
    })
  }

  fnDetailsQuickExpendsClick = async visibilityFeeTypes => {
    const { dataSource, bus } = this.props
    const { submitterId } = this.state
    const template =
      dataSource.currentSpecification ||
      dataSource?.form?.specificationId ||
      dataSource?.requisitionInfo?.defaultSpecification
    const isEBussCard = template?.configs?.find(item => item.isEBussCard)?.isEBussCard
    const value = await bus.getValue()
    const { details = [] } = value
    const importedDetailIds = details.map(el => getV(el, 'feeTypeForm.detailId'))
    return api.open('@quick-expends:ImportQuickExpendsToBill', {
      visibilityFeeTypes,
      submitterId: submitterId,
      billType: dataSource && dataSource.formType,
      isEBussCard,
      fromSourceType: 'createBill',
      importedDetailIds
    })
  }

  fnDetailsImportCSCClick = visibilityFeeTypes => {
    const { bus, dataSource = {} } = this.props
    const { id } = dataSource
    return bus.getValue().then(value => {
      const { details = [], submitterId } = value
      return api.open('@bills:ImportCivilServiceCardModal', {
        details,
        flowId: id,
        visibilityFeeTypes,
        valuation: this.valuation,
        submitterId
      })
    })
  }

  fnDealWidthAction = async action => {
    const { bus } = this.props
    const id = get(action, 'id') || get(action, 'payload.flow.id')
    // 这个地方是提交审批后必走的地方，在此处主动发起发票风险的计算申请,
    try {
      // @bills:get:flow:risk:warning 的数据必须等这个接口完成才能有
      await recordInvoiceRiskWarningFetchOnly(id) // 只做记录，不关注是否成功
    } catch { }
    const current = action.payload || {}
    const saveError = !!action.saveError
    if (action.error) {
      const failText = get(action, 'payload.msg', '')
      // 票据连号需要填写原因，输出判断条件
      let needSubmitRiskReason = false
      let needSubmitRiskReasonList = []
      if (failText && failText?.indexOf('发票违规，允许提交且需说明原因') !== -1) {
        needSubmitRiskReasonList = await getNeedSubmitRiskReasonList(id)
        // const obj = result[0]?.items.filter(el => el?.normType === 'INVOICE_SERIAL')
        // if (obj[0]?.control === 'ALLOW_SUBMIT_SHOW_RISK_EXPLAIN') {
        //   if (result[1] && result[1]?.value?.riskWarning?.length > 0) {
        //     const { riskWarning } = result[1]?.value
        //     riskWarning.forEach(el => {
        //       if (el?.controlName === '票据连号：' || el?.controlName === '存在连号票据：') {
        //         needSubmitRiskReason = true
        //       }
        //     })
        //   }
        // }
        needSubmitRiskReason = needSubmitRiskReasonList && needSubmitRiskReasonList.length > 0
      }
      const data = { state: 'failed', failText, id, saveError, needSubmitRiskReason, needSubmitRiskReasonList }
      bus.emit('flow:submit:error', data)
    } else {
      bus.emit('get:flow:state', { id, current })
    }
  }

  /**
   * @description saveFlow 提交送审 申请单的保存action
   * @param {*} value
   * @param {*} modify
   */
  fnSaveFlowForSubmit = (value, modify) => {
    const { saveFlow, writtenOffSummaryForMutiCurrency, dataSource, writtenOffSummaryForCrossCurrency } = this.props
    // 变更申请单的送审后端需要的变更标识
    const alterFlag = dataSource?.form?.alterFlag
    const { writtenOffDataObj, ...other } = writtenOffSummaryForMutiCurrency || {}
    const { profitAndLossSummary } = writtenOffSummaryForCrossCurrency || {}
    const formType = get(dataSource, 'formType')
    let id = get(value, 'id') || this.state.flowId
    if (writtenOffSummaryForCrossCurrency && formType === 'expense') {
      value.form = {
        ...value.form,
        profitAndLossSummary
      }
    }
    if (writtenOffSummaryForMutiCurrency && (formType === 'expense' || formType === 'receipt')) {
      value.form = { ...value.form, ...other }
    }
    value.id = id
    if (alterFlag) {
      value.form.alterFlag = alterFlag
    }
    // getWrittenOffValue
    const valueCopy = {}
    valueCopy.form = value.form
    valueCopy.name = 'freeflow.edit'
    if (value.params) {
      valueCopy.params = value.params
    }

    id && (valueCopy.id = id)
    const params = !!modify ? value : valueCopy
    !!modify
      ? saveFlow(params, { actionType: 'submit' }).then(action => {
        this.fnSetDetailFLowRelation(action)
        this.fnDealWidthAction({ id, ...action })
      })
      : saveFlow(params, { actionType: 'save', hiddenMsg: true }).then(action => {
        this.fnSetDetailFLowRelation(action)
        if (action.error) {
          this.fnDealWidthAction({ saveError: true, id, ...action })
        } else {
          if (!id) {
            const flowId = get(action, 'payload.flow.id')
            id = flowId
            value.id = flowId
            this.setState({ flowId })
          }
          value.form = get(action, 'payload.flow.form')
          saveFlow(value, { actionType: 'submit', hiddenMsg: true }).then(action => {
            this.fnSetDetailFLowRelation(action)
            this.fnDealWidthAction({ id, ...action })
          })
        }
      })
  }

  checkDetailsFeeTypeDetailId = details => {
    const {
      dataSource: { formType, state }
    } = this.props
    const feeTypeDetailIdMap = {}
    details.forEach((item, index) => {
      const it = item.feeTypeForm.detailId
      const currentFee = feeTypeDetailIdMap[it]
      const pos = index + 1
      if (!currentFee) {
        feeTypeDetailIdMap[it] = [pos]
      } else {
        feeTypeDetailIdMap[it] = currentFee.concat(pos)
      }
    })
    const hasCommonFeeTypeId = Object.keys(feeTypeDetailIdMap).find(item => feeTypeDetailIdMap[item]?.length > 1)
    const commonFeeType = feeTypeDetailIdMap[hasCommonFeeTypeId]
    return new Promise((resolve, reject) => {
      if (commonFeeType?.length && formType !== 'requisition' && state !== 'new') {
        // showModal.error({
        //   title: i18n.get('有重复费用'),
        //   content: i18n.get(`第${commonFeeType.join(',')}条费用重复，请删除后再操作！`),
        //   okText: i18n.get('知道了'),
        //   onOk: () => {
        //     reject('error')
        //   }
        // })
        trackFn('commonFeeType', {
          actionName: '有重复费用',
          source: window.__PLANTFORM__,
          commonFeeType: JSON.stringify(details)
        })
        resolve('ok')
      } else {
        resolve('ok')
      }
    })
  }

  /**
   * @description  saveFlow 存草稿 申请单的保存action
   * @param {*} value
   * @param {*} actionType
   * @returns
   */
  fnSaveFlowForSave = (value, actionType, onCloseModal) => {
    const { saveFlow, writtenOffSummaryForMutiCurrency, dataSource } = this.props
    // 变更申请单的送审后端需要的变更标识
    const alterFlag = dataSource?.form?.alterFlag
    if (alterFlag) {
      value.form.alterFlag = alterFlag
    }
    // 如果单据详情里存在checkingBillInfo，需要原封不动传给后端
    const checkingBillInfo = dataSource?.form?.checkingBillInfo
    if (checkingBillInfo) {
      value.form.checkingBillInfo = checkingBillInfo
    }
    const { writtenOffDataObj, ...other } = writtenOffSummaryForMutiCurrency || {}
    const formType = get(dataSource, 'formType')
    // this.fnMoveToRecordExpends()
    let val = cloneDeep(value)
    if (writtenOffSummaryForMutiCurrency && (formType === 'expense' || formType === 'receipt'))
      val.form = { ...value.form, ...other }
    const details = val?.form?.details || []
    const { flowId } = this.state
    let currentFlowId = get(value, 'id') || flowId
    const flowIdStr = isObject(currentFlowId) ? currentFlowId?.id : currentFlowId
    if (!val?.id && flowIdStr?.length > 0) {
      val.id = flowIdStr
    }
    return this.checkDetailsFeeTypeDetailId(details).then(res => {
      const endTime = new Date().getTime()
      const specification = this?.state?.currentSpecification
      endFlowFormDataCollectionStatistics({
        templateId: specification?.id,
        templateName: specification?.name
      })
      api?.logger?.info?.('单据保存+时间埋点', {
        specificationId: specification?.id,
        specificationName: specification?.name,
        flowId: val?.id,
        code: dataSource?.form?.code,
        form: val?.form,
        state: dataSource?.state,
        useTime: `${(endTime - this.startTime) / 1000}秒`
      })
      return saveFlow(val, actionType).then(action => {
        this.fnSetDetailFLowRelation(action)
        const billType = get(dataSource, 'formType')
        const baseType = ['settlement', 'reconciliation']
        if (!baseType.includes(billType)) {
          this.fnMoveToRecordExpends()
        }
        if (action.error) {
          try {
            let json = JSON.parse(action.payload.msg)
            if (json[0] && 'policyCheck' === json[0].type) {
              let { dataSource } = this.props
              dataSource.flowRulePerformLogs = { results: json }
              this.forceUpdate()
            }
          } catch (error) {
            return
          }
          return
        }
        this.fnDealWidthAction(action)
        related.clearRelatedData()
        let current = action.payload || {}
        current.flow && (current.id = current.flow.id)
        if (actionType.autoOpenDetail) {
          const { bus, remunerationBatchField } = this.props
          bus.invoke('bills:update:flow', { id: current.id })
          current.id &&
            api.emit('update:bill:open:remuneration', { id: current.id, bus, remunerationBatchField, current })
          return { state: 'success' }
        }
        api.emit('update:bill:list', actionType?.updateSelectLine ? current : null)
        onCloseModal && onCloseModal()
        return { state: 'success', current }
      })
    })
  }

  fnSetDetailFLowRelation = antion => {
    const { isQuickExpends } = this.state
    const id = get(antion, 'payload.flow.id')
    if (isQuickExpends && id) {
      // 查询单据明细是否是快速报销明细
      setDetailFLowRelation({
        flowId: id,
        detailSource: 'QUICK_EXPENSE'
      })
    }
  }

  fnMoveToRecordExpends = async () => {
    this.props.bus.getFieldsValue().then(async value => {
      const { dataSource, userInfo } = this.props
      const details = get(value, 'details')
      const feeData = this.RecordExpendsDetails
        ? this.RecordExpendsDetails
        : details
          ? await this.props.bus.invoke('get:moveTo:RecordExpendsDetails')
          : []
      const amountList = !!feeData.length ? feeData.filter(v => get(v, 'feeTypeForm.amount.standard')) : []
      const ownerId = get(dataSource, 'ownerId.id') || get(userInfo, 'staff.id')
      const submitterId = get(dataSource, 'form.submitterId.id')
      const id = get(dataSource, 'id')
      const formDetails = get(dataSource, 'form.details')

      if (!!amountList.length) {
        const {
          baseDataProperties,
          dataSource: { formType },
          multiplePayeesMode,
          payPlanMode
        } = this.props
        const { isQuickExpends } = this.state
        const key = isQuickExpends
          ? '@bills:put:feeDetail:moveTo:quickExpends'
          : '@bills:put:feeDetail:moveTo:recordExpends'
        const isDelPayInfo = multiplePayeesMode && !payPlanMode
        const params = this.RecordExpendsDetails
          ? fnGetDetailsParamsForChangeTemplate(
            amountList,
            baseDataProperties,
            formType,
            ownerId,
            id,
            formDetails,
            isDelPayInfo
          )
          : fnGetDetailsParams(amountList, baseDataProperties, formType, ownerId, id, formDetails, isDelPayInfo)
        api
          .invokeService(key, params, { submitterId })
          .then(() => {
            //showMessage.info(i18n.get('您选择的明细已被移至「随手记」，您可在「随手记」中继续编辑'))
          })
          .catch(e => {
            showMessage.error(e.message || e.msg)
          })
      }
    })
  }

  fnCreateNodesInstance(nodes) {
    let random = (min, max) => Math.floor(Math.random() * (max - min)) + min
    forEach(nodes, o => {
      o.id = `FLOW:${random(0, 2147483647)}:${random(0, 2147483647)}`
    })
    return nodes
  }

  fnGetWrittenOffResult(canWrittenOff = false, isSpecChange = false, mustWrittenOff = false) {
    let { bus } = this.props
    return new Promise((resolve, reject) => {
      return bus
        .invoke('get:writtenOff', canWrittenOff, isSpecChange, mustWrittenOff)
        .then(writtenOff => {
          if (writtenOff === 'cancel') {
            reject()
          }
          resolve(writtenOff)
        })
        .catch(error => {
          resolve()
        })
    })
  }

  // 将支付计划数据赋值到表单上
  preparePayPlanData = (formValue, isSubmit) => {
    if (!formValue.multiplePayeesMode) return
    if (!this.PayPlanStore) return
    if (this.PayPlanStore?.paymentPlanByApportion) {
      formValue.paymentPlanByApportion = true
    }
    const objs = toJS(this.PayPlanStore.objs)
    if (objs.find(item => item.dataLinkId)) {
      formValue.payPlan = objs
    } else {
      formValue.payPlan = formatPayPlanData(objs)
    }
    if (isSubmit) {
      const errorMsg = this.PayPlanStore.onUpdateErrorMsg()
      if (errorMsg) {
        showMessage.error(errorMsg)
        this.setState({ errorMsg })
        return true
      } else {
        this.setState({ errorMsg: '' })
        return false
      }
    }
  }

  checkCSCImportComplete = async details => {
    const errorMsg = []
    for (let detail of details) {
      const { feeTypeForm, specificationId, idx, feeTypeId } = detail
      const orders = get(feeTypeForm, 'orders', [])
      const ordersData = get(feeTypeForm, 'ordersData', [])
      const components = get(specificationId, 'components', [])
      const transactRecordIds = ordersData.map(v => v.transactRecordId)
      if (transactRecordIds?.length) {
        const res = await Fetch.GET(`/api/mall/v1/officialCard/[${transactRecordIds.join(',')}]`)
        const CSCData = res?.items || []
        let sumUseBalance = 0
        CSCData.forEach(v => {
          sumUseBalance = new Big(Number(sumUseBalance)).plus(new Big(Number(v.useBalance))).toString()
        })
        const checkFields = filterCSCFields(components)
        if (checkFields?.length && orders?.length) {
          let isComplete = true
          checkFields.forEach(v => {
            const defaultValueType = get(v, 'defaultValue.type')
            if (defaultValueType === 'officialCardSettlement' && !feeTypeForm[v.field]) {
              isComplete = false
            }
            if (defaultValueType === 'officialCardMoney' || v.field === 'amount') {
              const result = checkCSCMoney(feeTypeForm, v, sumUseBalance)
              if (result) {
                isComplete = false
              }
            }
          })
          if (!isComplete) {
            errorMsg.push(`第${idx + 1}条明细：${feeTypeId.name}`)
          }
        }
      }
    }
    return errorMsg
  }

  checkCSCImportEnable = formValue => {
    const { currentSpecification, referenceDataType } = this.state
    let CSCImportEnableByBill = true // true为不提示
    let CSCImportEnableByDetail = true // true为不提示
    const billTemplateIds = get(referenceDataType, 'referenceByImport.billTemplateIds', [])
    const feeTypeIds = get(referenceDataType, 'referenceByDetails.feeTypeIds', [])
    const billTemplateId = get(currentSpecification, 'originalId.id') || get(currentSpecification, 'originalId')
    const filterFeeTypes = []
    formValue?.details?.forEach(item => {
      const ordersData = get(item, 'feeTypeForm.ordersData')
      if (ordersData && ordersData.some(el => el.platform === 'transact')) {
        filterFeeTypes.push(item?.feeTypeId)
      }
    })
    if (filterFeeTypes.length) {
      if (referenceDataType?.referenceByImportEnable) {
        if (billTemplateIds.length && !billTemplateIds.includes(billTemplateId)) {
          CSCImportEnableByBill = false
        }
      } else {
        CSCImportEnableByBill = false
      }
      if (referenceDataType?.referenceByDetailsEnable) {
        if (feeTypeIds.length && filterFeeTypes.some(el => !feeTypeIds.includes(el.id))) {
          CSCImportEnableByDetail = false
        }
      } else {
        CSCImportEnableByDetail = false
      }
    }
    return { CSCImportEnableByBill, CSCImportEnableByDetail }
  }

  fnGetAutoRepaymentResult = () => {
    const { bus } = this.props
    return new Promise((resolve, reject) => {
      return bus
        .invoke('get:autoRepayment')
        .then(autoRepayment => {
          resolve(autoRepayment)
        })
        .catch(error => {
          resolve()
        })
    })
  }

  handleSaveBill = async (formValue, obj = {}, onCloseModal) => {
    const { autoOpenDetail = false, updateSelectLine = true } = obj
    const { dataSource, baseDataProperties, baseDataPropertiesMap, bus, customizeQueryPower } = this.props
    const { currentSpecification, template, isAutoRepayment, timeField } = this.state
    startSaveFlowPerformanceStatistics()
    this.preparePayPlanData(formValue, false)
    let value = parseFormValueAsParam(formValue, currentSpecification, dataSource, baseDataProperties)
    //保存的时候，将自动生成费用明细标识一起保存
    let fromDetails = formValue?.details || []
    let valueDetails = value?.form?.details || []
    if (fromDetails.length > 0) {
      for (let i = 0; i < fromDetails.length; i++) {
        if (fromDetails[i]?.feeTypeForm?.isAutoDetail) {
          valueDetails[i].feeTypeForm.isAutoDetail = fromDetails[i]?.feeTypeForm?.isAutoDetail
        }
      }
    }
    if (timeField && fromDetails?.length && fromDetails.find(el => el.shouldSaveFeetype)) {
      showMessage.error(i18n.get('币种汇率发生变化，请点击费用明细中重新保存后提交'))
      return
    }
    if (customizeQueryPower) {
      const checkCustomizeResult = await checkCustomizeSubmitValue(
        value.form,
        baseDataPropertiesMap,
        bus,
        template,
        currentSpecification?.id,
        dataSource.state
      )
      const isCheckCustomizeDetailCalAttr = get(checkCustomizeResult, 'attrValue.detailInfo.isCheckDetailCalAttr')
      if (isCheckCustomizeDetailCalAttr) {
        bus.setFieldsValue({ details: get(checkCustomizeResult, 'attrValue.detailInfo.details') })
        bus.getValueWithValidate(['details'])
        return
      }
    }
    const submitterId = value.form?.submitterId

    if (isAutoRepayment) {
      let autoRepayment = await this.fnGetAutoRepaymentResult()
      for (let i of autoRepayment) {
        delete i?.amount
        delete i?.foreignCurrencyLoan
        delete i?.remain
        i.repaymentDate = new Date().getTime()
      }
      value.params = { loanManualRepayment: autoRepayment }
    }
    return this.fnGetWrittenOffResult().then(writtenOff => {
      if (writtenOff && writtenOff.length) {
        return checkLoanPackage(writtenOff, submitterId)
          .then(() => {
            const paybackAbility = currentSpecification.configs.find(
              v => v.ability === 'receipt' && v.summarySelect === summarySelectType.repay
            )
            return this.checkPackbackMoney(writtenOff, paybackAbility, formValue.details)
          })
          .then(_ => {
            if (value.params) {
              value.params = { ...value.params, loanWrittenOff: writtenOff }
            } else {
              value.params = { loanWrittenOff: writtenOff }
            }
            return this.fnSaveFlowForSave(
              value,
              { actionType: 'save', hiddenMsg: false, autoOpenDetail, updateSelectLine },
              onCloseModal
            )
          })
          .catch(err => {
            console.log(err)
          })
      } else {
        return this.fnSaveFlowForSave(
          value,
          { actionType: 'save', hiddenMsg: false, autoOpenDetail, updateSelectLine },
          onCloseModal
        )
      }
    })
  }

  handleSwitchMessageChange = data => {
    // 先清除，否则会找旧模版的提示
    this.setState({
      switchTips: {}
    })
    const tips = {}
    data.messageData.map(i => {
      if (i.attribute === 'setip') {
        tips[i.onField] = i.result
      }
    })
    this.setState({
      switchTips: tips
    })
  }

  fnCheckPayeeByZero = formValue => {
    const { loanMoney, details = [], multiplePayeesMode, payPlanMode, payeePayPlan, payeeId } = formValue
    let warningMsg = null
    const targetTotalMoney = this.PayPlanStore?.targetTotalMoney
    const greaterThanZero = targetTotalMoney ? targetTotalMoney * 1 > 0 : false
    const objs = toJS(this.PayPlanStore?.objs)
    // payeeId 单收款人
    if (!multiplePayeesMode && !payeeId?.id && greaterThanZero) {
      warningMsg = '支付金额不为0时收款信息必填'
    }
    if (multiplePayeesMode) {
      // 多收款人 multiplePayeesMode   payPlanMode   payeePayPlan
      // 明细      true                 false          false
      // 金额      true                 true           false
      // 收款      true                 false          true
      // 多收款人 金额大于0时都判断是否有支付计划， 明细和按收款信息汇总明细金额需单独判断明细中金额大于0时是否有收款信息
      const detailsOnly = !payPlanMode && !payeePayPlan
      const payeeOnly = !payPlanMode && payeePayPlan
      if (detailsOnly || payeeOnly) {
        details.forEach(el => {
          const standard = get(el, 'feeTypeForm.amount.standard')
          const feeDetailPayeeId = get(el, 'feeTypeForm.feeDetailPayeeId.id')
          if (standard && standard * 1 > 0 && !feeDetailPayeeId) {
            warningMsg = '费用明细金额不为0，收款信息必填'
          }
        })
      } else if (greaterThanZero && !objs.length) {
        warningMsg = '支付金额不为0时收款信息必填'
      }
    }
    if (loanMoney && loanMoney.standard * 1 > 0 && !formValue?.payeeId?.id) {
      warningMsg = '支付金额不为0时收款信息必填'
    }
    return warningMsg
  }

  checkApportions = formValue => {
    return new Promise((resolve, reject) => {
      const details = get(formValue, 'details')
      details &&
        details.forEach(v => {
          if (v.isRemuneration) {
            return
          }
          const {
            specificationId: { components },
            feeTypeForm
          } = v
          const { open } = components.find(v => v.type === 'apportions') || {}
          if (open && (!feeTypeForm.apportions || !feeTypeForm.apportions.length)) {
            return reject(i18n.get('消费明细分摊未填写'))
          }
        })
      return resolve(formValue)
    })
  }
  handleSubmitBill = async formValue => {
    const {
      dataSource,
      baseDataProperties,
      baseDataPropertiesMap,
      bus,
      customizeQueryPower,
      civilServiceCard
    } = this.props
    const {
      currentSpecification,
      template,
      switchTips,
      generationRule,
      isAutoRepayment,
      travelTemplateList,
      timeField,
      hiddenFields
    } = this.state
    const travelPlanning = getV(formValue, 'travelPlanning', [])
    startSaveFlowPerformanceStatistics()
    if (travelPlanning?.length) {
      const sceneList = travelTemplateList.filter(el => el?.sceneTypeField === 'open_required').map(v => v.name)
      const errorList = []
      travelPlanning.forEach(item => {
        if (sceneList.includes(item.type) && !item?.sceneList) {
          if (!errorList.includes(item.type)) {
            errorList.push(i18n.get(item.type))
          }
        }
      })
      if (errorList?.length) {
        showMessage.warning(i18n.get('请填写行程规划中{__k0}场景字段信息', { __k0: errorList.join('、') }))
        return
      }
    }
    const { loanMoney, payMoney, details = [] } = formValue
    const hasDraftDetails = details.find(v => v?.feeTypeForm?.detailStatus === 'draft')
    if (hasDraftDetails) {
      showMessage.error(i18n.get('有草稿状态的明细，请先保存明细后再提交'))
      return
    }
    const pay = currentSpecification.configs.find(v => v.ability === 'pay')
    const optionalPayeeByZero = pay && pay?.optionalPayeeByZero
    if (optionalPayeeByZero) {
      formValue.optionalPayeeByZero = optionalPayeeByZero
      let warningMsg = this.fnCheckPayeeByZero(formValue)
      if (warningMsg) {
        showMessage.warning(i18n.get(warningMsg))
        return
      }
    }
    if (loanMoney) {
      if (loanMoney.standard * 1 < 0) {
        showMessage.error(i18n.get(`支付金额不得小于0`))
        return
      }
    }
    if (payMoney) {
      if (payMoney.standard * 1 < 0) {
        showMessage.error(i18n.get(`支付金额不得小于0`))
        return
      }
    }
    if (timeField && details?.length && details.find(el => el.shouldSaveFeetype)) {
      showMessage.error(i18n.get('币种汇率发生变化，请点击费用明细中重新保存后提交'))
      return
    }
    if (civilServiceCard) {
      const { CSCImportEnableByBill, CSCImportEnableByDetail } = this.checkCSCImportEnable(formValue)
      if (!CSCImportEnableByBill && !CSCImportEnableByDetail) {
        showMessage.warning(i18n.get('该模板或费用类型不支持导入公务卡数据'))
        return
      }
      if (details?.length) {
        const result = await this.checkCSCImportComplete(details)
        if (result?.length) {
          const contentStyle = {
            textAlign: 'left',
            marginTop: 20,
            maxHeight: 300,
            overflow: 'auto'
          }
          const msg = result?.map(el => <p>{el}</p>)
          const content = <div style={contentStyle}>{msg}</div>
          showModal.warning({
            title: i18n.get('以下明细公务卡相关字段缺失或错误'),
            content,
            okText: i18n.get('我知道了'),
            onOk: () => { }
          })
          return
        }
      }
    }

    const rules = currentSpecification?.rules || []
    const switcherRules = rules.filter(item => item.code === '11' && item.standard === true)
    //判断开关类型的字段，如果设置了只有开启才能提交单据的限制，则给出提出，并且禁止提交
    const errorSwitch = switcherRules.filter(o => !formValue[o.field])
    const errorFields = switcherRules
      .filter(o => !formValue[o.field])
      .map(o => this._getComponentLabelByField(currentSpecification.components, o.field))
      .join(',')
    if (errorSwitch?.length) {
      const errorMessage = switchTips[errorSwitch?.[0]?.field]
      if (switchTips && errorMessage) {
        showMessage.error(errorMessage)
        return
      } else {
        if (errorFields) {
          showMessage.error(i18n.get(`请勾选「{__k0}」才能提交`, { __k0: errorFields }))
          return
        }
      }
    }
    //自动生成费用明细校验
    if (generationRule?.length > 0 && details?.length > 0) {
      let subData = {
        form: { ...formValue, specificationId: currentSpecification?.id },
        billType: currentSpecification?.type,
        currentEditField: '',
        startIdx: details.length
      }
      if (await this.getAutoDetail(subData, bus, details)) {
        return
      }
    }
    const result = this.preparePayPlanData(formValue, true)
    if (result) return

    let value = parseSubmitParamAsFormValue(
      formValue,
      currentSpecification,
      dataSource,
      baseDataProperties,
      template,
      hiddenFields
    )
    if (dataSource?.openFrom === 'flow-preview') {
      // 审批预览 直接请求预览接口
      return this.checkSupplementIdentity(value, dataSource)
    }
    if (dataSource?.openFrom === 'permit-form') {
      return { state: 'success', value }
    }
    const checkResult = await checkSubmitValue(value.form, baseDataPropertiesMap, bus, template)
    try {
      // 在这之后验证 分摊
      const res = await this.checkApportions(formValue)
    } catch (error) {
      console.log('[ 分摊验证错误 error ] >', error)
      showMessage.error(error)
      return
    }
    const isCheckDetailCalAttr = get(checkResult, 'attrValue.detailInfo.isCheckDetailCalAttr')
    if (isCheckDetailCalAttr) {
      bus.setFieldsValue({ details: get(checkResult, 'attrValue.detailInfo.details') })
      bus.getValueWithValidate(['details'])
      return
    }
    const autoCalculateValue = get(checkResult, 'resultValue')
    if (autoCalculateValue?.details) {
      if (autoCalculateValue.details.filter(v => v.needUpdate).length) {
        showMessage.error(i18n.get(`请重新保存相关明细`))
        return
      }
      value = parseSubmitParamAsFormValue(
        { ...value.form, details: autoCalculateValue.details },
        currentSpecification,
        dataSource,
        baseDataProperties
      )
    }
    // 检查费用明细模板，当文本配置了超额后，有发票前提下，符合条件就必填，需提示`请重新保存相关明细`
    if (details?.length > 0) {
      let hasExceedComErr = []
      details?.forEach(item => {
        const { feeTypeForm, specificationId } = item
        const textComponents = (specificationId?.components || []).reduce((ts, t) => {
          const type = t?.type
          const amountChecked = get(t, 'exceedConfigs.amountChecked', false)
          const invoiceAmount = get(t, 'exceedConfigs.invoiceAmount', 0)
          const typeArr = ['text', 'textarea']
          if (typeArr.indexOf(type) > -1 && amountChecked) {
            let obj = {
              field: t.field,
              label: t.label,
              invoiceAmount: invoiceAmount
            }
            ts.push(obj)
          }
          return ts
        }, [])
        const invoices = feeTypeForm?.invoiceForm?.invoices || []
        if (textComponents.length > 0 && invoices.length > 0) {
          //单个明细发票金额面值集合
          let amountArr = []
          invoices.forEach(val => {
            const {
              master: { form }
            } = val
            let money = form?.E_system_发票主体_价税合计?.standard
            amountArr.push(money)
          })
          textComponents.forEach(item => {
            let invoiceAmount = item.invoiceAmount
            let label = item.label
            let hasExceed = false
            amountArr.forEach(num => {
              if (Number(num) > Number(invoiceAmount)) {
                hasExceed = true
              }
            })
            //某个发票面额值大于相应文本超额值
            if (
              hasExceed &&
              (feeTypeForm[item.field] === '' ||
                feeTypeForm[item.field] === null ||
                feeTypeForm[item.field] === undefined)
            ) {
              hasExceedComErr.push(label)
            }
          })
        }
      })
      if (hasExceedComErr.length > 0) {
        showMessage.error(i18n.get(`请重新保存相关明细`))
        return
      }
    }

    if (customizeQueryPower) {
      const checkCustomizeResult = await checkCustomizeSubmitValue(
        value.form,
        baseDataPropertiesMap,
        bus,
        template,
        currentSpecification?.id,
        dataSource.state
      )
      const isCheckCustomizeDetailCalAttr = get(checkCustomizeResult, 'attrValue.detailInfo.isCheckDetailCalAttr')
      if (isCheckCustomizeDetailCalAttr) {
        bus.setFieldsValue({ details: get(checkCustomizeResult, 'attrValue.detailInfo.details') })
        bus.getValueWithValidate(['details'])
        return
      }
      const customizeCalculateValue = get(checkCustomizeResult, 'resultValue')
      if (customizeCalculateValue?.details) {
        if (customizeCalculateValue.details.filter(v => v.needUpdate).length) {
          showMessage.error(i18n.get(`请重新保存相关明细`))
          return
        }
        value = parseSubmitParamAsFormValue(
          { ...value.form, details: customizeCalculateValue.details },
          currentSpecification,
          dataSource,
          baseDataProperties
        )
      }
    }

    if (isAutoRepayment) {
      let autoRepayment = await this.fnGetAutoRepaymentResult()
      if (autoRepayment && autoRepayment?.length > 0) {
        let result = await this.checkAutoRepaymentDataComplete(autoRepayment)
        if (result) {
          return
        } else {
          let result1 = await this.checkAutoRepaymentSingleMoney(autoRepayment)
          if (result1) {
            return
          } else {
            let result2 = await this.checkAutoRepaymentMoney(autoRepayment)
            if (result2) {
              return
            } else {
              value.params = { loanManualRepayment: autoRepayment }
            }
          }
        }
      }
    }

    const applyAbility = currentSpecification.configs.find(v => v.ability === 'apply')
    //checkTravelCloseLoop【校验申请单行程闭环状态】和 checkTravelOrderConfirm【校验申请单关联订单确认状态】配置
    const travelCloseLoop = applyAbility?.checkTravelCloseLoop
    const travelOrderConfirm = applyAbility?.checkTravelOrderConfirm
    //单据模板是否开启了校验
    if (!!travelCloseLoop?.openCheck || !!travelOrderConfirm?.openCheck) {
      //如果只关联了一张申请单，才需要进行校验，多张申请单不校验
      if (
        formValue?.hasOwnProperty('expenseLink') ||
        (formValue?.hasOwnProperty('expenseLinks') && formValue?.expenseLinks?.length === 1)
      ) {
        try {
          //扩展中心是否开启了【行程闭环校验】，差旅壹号是否开启了【支持员工确认订单并通知差旅壹号】
          let res1 = await upgradeOrderMicro({ type: 'travelCloseLoopConfig' })
          let res2 = await upgradeOrderMicro({ type: 'travelOneOrderConfirmConfig' })
          let closeLoopOpen = res1?.value?.contextDetail?.openStatus
          let orderConfirmOpen = res2?.value?.contextDetail?.openStatus
          if (!!closeLoopOpen || !!orderConfirmOpen) {
            let flowId
            if (formValue?.hasOwnProperty('expenseLinks')) {
              flowId = formValue?.expenseLinks[0]?.id
            } else if (formValue?.hasOwnProperty('expenseLink')) {
              flowId = formValue?.expenseLink?.id
            }
            //获取关联的申请单的订单闭环和确认状态
            let travelResult = await getFlowTravelResult({ flowId })
            //行程闭环校验
            let result1 = await this.checkTravelManagement(travelResult, travelCloseLoop, closeLoopOpen)
            if (result1) {
              return
            } else {
              //订单确认校验
              let result2 = await this.checkTravelOrderConfirm(travelResult, travelOrderConfirm, orderConfirmOpen)
              if (result2) {
                return
              }
            }
          }
        } catch (err) { }
      }
    }

    let writtenOff =
      currentSpecification.configs.find(v => v.ability === 'writtenOff') || currentSpecification.type === 'receipt'
    let paybackAbility = currentSpecification.configs.find(
      v => v.ability === 'receipt' && v.summarySelect === summarySelectType.repay
    )
    let canWrittenOff = !!writtenOff
    const mustWrittenOff = get(writtenOff, 'mustWrittenOff', false)
    const submitterId = value?.form?.submitterId
    // 提交送审, 检查是否需要校验核销的费用类型
    const isForbiddenByFeeType =
      get(currentSpecification, 'configs', []).filter(config => config.ability === 'expense')[0]
        ?.isForbiddenByFeeType || false
    if (isForbiddenByFeeType) {
      let isNeedCheck = false
      let writtenOffArrTemp = []
      // 这里接口拿到的 writtenOff 不具有可靠性，比如在关联多个申请单时
      if (this.writtenOffFromEventBus && this.writtenOffFromEventBus.length) {
        writtenOffArrTemp = (this.writtenOffFromEventBus || []).map(writtenOffItem => {
          if (!writtenOffItem.flowId && writtenOffItem.loanInfoId && writtenOffItem.loanInfoId.flowId) {
            writtenOffItem.flowId = writtenOffItem.loanInfoId.flowId
          }
          return writtenOffItem
        })
      }
      const writtenOffFlowIds = writtenOffArrTemp.map(item => item.flowId) // 核销中关联的 flowIds
      if (formValue?.expenseLinks && formValue?.expenseLinks?.length) {
        // 报销单设置了关联申请为按明细核销
        const expenseLinks = get(formValue, 'expenseLinks', []) // 关联申请单的 flowId
        const expenseLinkFlowIdArr = expenseLinks.map(item => {
          if (typeof item.flowId === 'object') {
            return item.flowId.id
          }
          return item.flowId
        })
        isNeedCheck = expenseLinkFlowIdArr.some(item => writtenOffFlowIds.includes(item))
      } else {
        // 报销单设置了关联申请为按整体核销
        const expenseLinkFlowId = get(formValue, 'expenseLink.flowId') // 关联申请单的 flowId
        const flowId = typeof expenseLinkFlowId === 'object' ? expenseLinkFlowId.id : expenseLinkFlowId
        isNeedCheck = writtenOffFlowIds.includes(flowId)
      }
      if (isNeedCheck) {
        const { details: formValueDetails = [] } = formValue
        const expenseLinkDetailsFeeTypeArr = this.expenseLinkDetails.map(detailItem => detailItem.feeTypeId.code)
        const unContianerFeeType = formValueDetails.some(
          detailItem => !expenseLinkDetailsFeeTypeArr.includes(detailItem.feeTypeId.code)
        )
        if (unContianerFeeType) {
          showMessage.error(i18n.get(`核销费用类型与所核销申请费用类型不一致`))
          return
        }
      }
    }
    this.setLegalEntity(value)
    return this.fnGetWrittenOffResult(canWrittenOff, false, mustWrittenOff).then(writtenOff => {
      if (writtenOff && writtenOff.length) {
        return checkLoanPackage(writtenOff, submitterId)
          .then(() => {
            return this.checkPackbackMoney(writtenOff, paybackAbility, formValue.details)
          })
          .then(_ => {
            if (value.params) {
              value.params = { ...value.params, loanWrittenOff: writtenOff }
            } else {
              value.params = { loanWrittenOff: writtenOff }
            }
            return this.checkSupplementIdentity(value, dataSource)
          })
          .catch(err => {
            console.log(err)
          })
      } else {
        return this.checkSupplementIdentity(value, dataSource)
      }
    })
  }

  // 报销单使用多收款时，将单据上的法人实体赋值到支付计划上
  setLegalEntity = data => {
    const { form, formType } = data
    if (formType === 'expense') {
      const legalEntity = get(form, '法人实体')
      const multiplePayeesMode = get(form, 'multiplePayeesMode')
      if (multiplePayeesMode && legalEntity) {
        form.payPlan.forEach(el => {
          if (el?.dataLinkForm && !el?.dataLinkForm?.E_system_支付计划_legalEntity) {
            el.dataLinkForm.E_system_支付计划_legalEntity = legalEntity
          }
        })
      }
    }
  }

  getAutoDetail = (subData, bus, details = []) => {
    return new Promise((resolve, reject) => {
      api.dispatch(getAutoGenerationFeeDetail(subData)).then(async res => {
        let detailsFlag = false
        let detailsAuto = res?.items || []
        if (detailsAuto && detailsAuto.length === 0) {
          resolve(false)
        } else {
          let countByAuto = countBy(detailsAuto, obj => obj?.feeTypeId?.id)
          let countByForm = countBy(details, obj => obj?.feeTypeId?.id)
          if (Object.keys(countByAuto).length > Object.keys(countByForm).length) {
            detailsFlag = true
          }
          for (let key1 in countByAuto) {
            if (countByAuto[key1] !== countByForm[key1]) {
              detailsFlag = true
            }
          }
          if (detailsFlag) {
            showModal.confirm({
              title: i18n.get('自动生成费用明细结果与规则不符，是否更新？'),
              cancelText: i18n.get('取消'),
              okText: i18n.get('确定'),
              onOk: () => {
                reject(true)
                let notAutoDetails = details.filter(i => {
                  return !i?.feeTypeForm?.isAutoDetail
                })
                let subDetails = this.ResetAutoGenerationFeeIdx(notAutoDetails.concat(detailsAuto))
                bus.emit('dynamic:detail:change', subDetails)
              },
              onCancel: () => {
                resolve(false)
              }
            })
          } else {
            resolve(false)
          }
        }
      })
    })
  }

  //校验还款金额是否大于费用明细
  checkPackbackMoney = (loanWrittenOff, paybackAbility, details = []) => {
    return new Promise((resolve, reject) => {
      if (paybackAbility) {
        let paybackTotalMoney = 0
        let detailTotal = 0
        loanWrittenOff.forEach(i => {
          paybackTotalMoney += Number(i.amount)
        })
        details?.forEach(j => {
          let amt = j.feeTypeForm.amount.standard
          detailTotal += Number(amt)
        })
        if (paybackTotalMoney != detailTotal) {
          if (paybackAbility?.feeDetailAndLoan === 1) {
            showModal.info({
              title: i18n.get('费用明细与还款金额不一致，请修改后提交'),
              okText: i18n.get('确定')
            })
            reject()
          } else if (paybackAbility?.feeDetailAndLoan === 2) {
            showModal.confirm({
              title: i18n.get('费用明细金额与还款金额不一致，是否继续提交单据？'),
              cancelText: i18n.get('取消'),
              okText: i18n.get('确定'),
              onOk: () => {
                resolve()
              },
              onCancel: () => {
                reject()
              }
            })
          } else {
            resolve()
          }
        } else {
          resolve()
        }
      } else {
        resolve()
      }
    })
  }

  checkSupplementIdentity = (value, dataSource) => {
    let writtenOffData = value.params || {}
    let form = value.form
    // 多币种核销时，后端无法校验，需要去掉这块的数据
    const { writtenOffSummaryForMutiCurrency } = this.props
    if (writtenOffSummaryForMutiCurrency) {
      const { writtenOffDataObj, ...other } = writtenOffSummaryForMutiCurrency || {}
      form = { ...form, ...other }
    }
    return this.hanldeCalPlanInstance({ ...value, form, params: writtenOffData }, dataSource).then(result => {
      if (result === 'cancel') return
      let loanManualRepayment = value?.params?.loanManualRepayment
      if (loanManualRepayment && loanManualRepayment?.length > 0) {
        for (let i of loanManualRepayment) {
          delete i?.amount
          delete i?.foreignCurrencyLoan
          delete i?.remain
          delete i?.foreignRemain
          delete i?.foreignAmount
          delete i?.uid
          i.repaymentDate = new Date().getTime()
        }
      }
      delete value.flowPlanConfigId
      const { flowNodes, urgent, sensitiveContent, sensitiveAttachment } = result
      const params = { name: 'freeflow.submit', ...value, ...urgent }
      if (sensitiveContent) {
        params.sensitiveContent = sensitiveContent
      }
      if (sensitiveAttachment?.length) {
        params.sensitiveAttachment = sensitiveAttachment
      }
      if (flowNodes) {
        params.nodes = calculateFlowNodes(flowNodes)
      }
      return this.handleSubmitWrapper(params)
    })
  }

  hanldeCalPlanInstance = (value, dataSource) => {
    const { bus } = this.props
    const alterFlag = get(dataSource, 'form.alterFlag')
    const nextId = get(dataSource, 'plan.submitNode.nextId', 'SUBMIT')
    const isResubmit = dataSource?.state === 'draft' ? true : nextId === 'SUBMIT'
    const submitNodes = dataSource?.logs?.filter(v => v.action === 'freeflow.submit') || []
    const { sensitiveContent, sensitiveAttachment } = submitNodes[submitNodes.length - 1]?.attributes || {}
    if (!isResubmit) {
      const { urgent, isSensitive, isRequired } = dataSource?.plan?.submitNode ?? {}
      if (!isSensitive) return Promise.resolve({})
      return api.open('@bills:FlowConfigModal', {
        fetch: () => {
          return Promise.resolve({
            dataSource,
            onlySensitive: true,
            data: {
              urgent,
              isSensitive,
              isRequired,
              sensitiveContent,
              sensitiveAttachment
            }
          })
        }
      })
    }
    if (dataSource?.openFrom === 'flow-preview') {
      // 审批预览替换预览 id
      value.flowPlanConfigId = dataSource.flowPlanConfigId
    }

    return new Promise(async (resolve, reject) => {
      const res = await api.open('@bills:FlowConfigModal', {
        bus,
        fetch: () => {
          return new Promise((resolve2, _reject) => {
            this.props.calPlanInstance(value, alterFlag).then(action => {
              if (action?.error) {
                bus.emit('closeFlowConfigModal')
                return reject()
              }
              if (dataSource?.openFrom === 'flow-preview') {
                bus.emit('clear:status')
              }
              const resp = action.payload
              const details = value?.form?.details || []
              return this.checkDetailsFeeTypeDetailId(details).then(() => {
                const nodes = this.fnCreateNodesInstance(resp?.value?.nodes)
                const { urgent, isSensitive, isRequired } = resp?.value?.submitNode ?? {}
                return resolve2({
                  dataSource,
                  data: {
                    flowNodes: nodes,
                    urgent,
                    isSensitive,
                    isRequired,
                    sensitiveContent,
                    sensitiveAttachment
                  }
                })
              })
            })
          })
        }
      })
      resolve(res)
    })
  }
  defer() {
    let resolve, reject
    const promise = new Promise((res, rej) => {
      resolve = res
      reject = rej
    })
    return { promise, resolve, reject }
  }

  checkAutoRepaymentDataComplete = autoRepayment => {
    const { promise, resolve, reject } = this.defer()
    //数据完整性校验
    let isComplete = false
    let reg = /(^[1-9]{1}\d*$)|(^\d*\.\d{1,2}$)/
    outer: for (let i = 0; i < autoRepayment.length; i++) {
      let item = autoRepayment[i]
      if (item?.foreignCurrencyLoan) {
        if (
          item?.loanInfoId &&
          item?.manualRepaymentMoney?.standard &&
          item?.manualRepaymentMoney?.foreign &&
          item?.manualRepaymentMoney?.rate &&
          item?.accountId &&
          reg.test(item?.manualRepaymentMoney?.standard) &&
          reg.test(item?.manualRepaymentMoney?.foreign) &&
          reg.test(item?.manualRepaymentMoney?.rate)
        ) {
          isComplete = true
        } else {
          isComplete = false
          break outer
        }
      } else {
        if (
          item?.loanInfoId &&
          item?.manualRepaymentMoney?.standard &&
          item?.accountId &&
          reg.test(item?.manualRepaymentMoney?.standard)
        ) {
          isComplete = true
        } else {
          isComplete = false
          break outer
        }
      }
    }
    if (!isComplete) {
      showModal.info({
        title: i18n.get('还款信息填写不完整或有误'),
        okText: i18n.get('确定')
      })
      reject(true)
    } else {
      resolve(false)
    }
    return promise
  }

  checkAutoRepaymentSingleMoney = autoRepayment => {
    const { currentSpecification } = this.state
    const { promise, resolve, reject } = this.defer()
    const isForbiddenSubmit = get(
      get(currentSpecification, 'configs', []).find(v => v.ability === 'allowRepayment'),
      'isForbiddenSubmit',
      true
    )

    //单笔校验,先校验是否有大于0的，是的话直接禁止提交；再校验小于0的情况，按配置的强弱看是否允许提交
    let isOver = false,
      isLess = false
    for (let item of autoRepayment) {
      if (item?.foreignCurrencyLoan) {
        if (Number(item?.manualRepaymentMoney?.foreign) + Number(item?.foreignAmount) > Number(item?.foreignRemain)) {
          isOver = true
          break
        }
      } else {
        if (Number(item?.manualRepaymentMoney?.standard) + Number(item?.amount) > Number(item?.remain)) {
          isOver = true
          break
        }
      }
    }
    if (isOver) {
      showModal.info({
        title: i18n.get('还款金额加核销金额不能大于借款余额'),
        okText: i18n.get('确定')
      })
      reject(true)
    } else {
      for (let item of autoRepayment) {
        if (item?.foreignCurrencyLoan) {
          if (Number(item?.manualRepaymentMoney?.foreign) + Number(item?.foreignAmount) < Number(item?.foreignRemain)) {
            isLess = true
            break
          }
        } else {
          if (Number(item?.manualRepaymentMoney?.standard) + Number(item?.amount) < Number(item?.remain)) {
            isLess = true
            break
          }
        }
      }
      if (isLess) {
        if (isForbiddenSubmit === 'Y') {
          showModal.confirm({
            title: i18n.get('未结清借款'),
            content: i18n.get('您有借款未结清，是否继续提交单据？'),
            cancelText: i18n.get('取消'),
            okText: i18n.get('确定'),
            onOk: () => {
              resolve(false)
            },
            onCancel: () => {
              reject(true)
            }
          })
        } else {
          showModal.info({
            title: i18n.get('未结清借款'),
            content: i18n.get('您有借款未结清，请将借款结清后再提交单据'),
            okText: i18n.get('确定')
          })
          reject(true)
        }
      } else {
        resolve(false)
      }
    }
    return promise
  }

  checkAutoRepaymentMoney = autoRepayment => {
    const { bus } = this.props
    const { promise, resolve, reject } = this.defer()
    bus.invoke('get:pay:money').then(payMoney => {
      //payMoney: 支付金额
      //整体校验
      let remainTotal = 0 //借款包剩余总额，即可核销总额
      let amountTotal = 0 //借款包核销总额
      let repaymentTotal = 0 //总还款金额
      for (let val of autoRepayment) {
        remainTotal += Number(val?.remain)
        amountTotal += Number(val?.amount)
        repaymentTotal += Number(val?.manualRepaymentMoney?.standard)
      }
      if (Number(getMoney(payMoney)) - repaymentTotal > 0 && remainTotal - amountTotal - repaymentTotal > 0) {
        showModal.confirm({
          title: i18n.get('费用未核销借款'),
          content: i18n.get('您可能有补贴费用，请确认，是否需要核销借款，还是直接支付？'),
          cancelText: i18n.get('取消'),
          okText: i18n.get('确定'),
          onOk: () => {
            resolve(false)
          },
          onCancel: () => {
            reject(true)
          }
        })
      } else {
        resolve(false)
      }
    })
    return promise
  }

  //申请单行程未闭环校验
  checkTravelManagement = (travelResult, travelCloseLoop, closeLoopOpen) => {
    const { promise, resolve, reject } = this.defer()
    if (!!!travelCloseLoop?.openCheck || !!!closeLoopOpen) {
      //单据模板上未开启校验或者扩展中心未开启行程闭环校验，都不需要校验
      resolve(false)
    } else {
      if (!travelResult?.isCloseLoop) {
        if (!!travelCloseLoop?.isForbidSubmit) {
          showModal.info({
            title: i18n.get('申请单行程未闭环'),
            content: i18n.get('申请单上的行程未闭环，禁止提交单据'),
            okText: i18n.get('确定'),
            onOk: () => {
              resolve(true)
            }
          })
        } else {
          showModal.confirm({
            title: i18n.get('申请单行程未闭环'),
            content: i18n.get('申请单上的行程未闭环，是否继续提交单据？'),
            cancelText: i18n.get('取消'),
            okText: i18n.get('确定'),
            onOk: () => {
              resolve(false)
            },
            onCancel: () => {
              resolve(true)
            }
          })
        }
      } else {
        resolve(false)
      }
    }
    return promise
  }

  //申请单关联订单确认校验
  checkTravelOrderConfirm = (travelResult, travelOrderConfirm, orderConfirmOpen) => {
    const { promise, resolve, reject } = this.defer()
    if (!!!travelOrderConfirm?.openCheck || !!!orderConfirmOpen) {
      //单据模板上未开启校验或者差旅壹号未开启订单确认，都不需要校验
      resolve(false)
    } else {
      if (!travelResult?.isOrderConfirm) {
        if (!!travelOrderConfirm?.isForbidSubmit) {
          showModal.info({
            title: i18n.get('申请单关联订单未全部确认'),
            content: i18n.get('申请单关联订单未全部确认，禁止提交单据'),
            okText: i18n.get('确定'),
            onOk: () => {
              resolve(true)
            }
          })
        } else {
          showModal.confirm({
            title: i18n.get('申请单关联订单未全部确认'),
            content: i18n.get('申请单关联订单未全部确认，是否继续提交单据？'),
            cancelText: i18n.get('取消'),
            okText: i18n.get('确定'),
            onOk: () => {
              resolve(false)
            },
            onCancel: () => {
              resolve(true)
            }
          })
        }
      } else {
        resolve(false)
      }
    }
    return promise
  }

  handleSubmitWrapper = (params, modify) => {
    const { isModify } = this.props
    if (isModify) {
      // 已进入审批之后的编辑状态不进行拦截提示
      return this.handleSubmit(params, modify)
    }
    // 异地单据寄收管理 校验
    return new Promise((resolve, reject) => {
      if (this.props.ExpressCharge) {
        const { receivingExceptData: receivingExcepData = [], hasConfig, isSubmit, tipsMessage } = this.submitControl
        if (hasConfig && receivingExcepData?.length) {
          showModal.warning({
            title: <b>{i18n.get('收单异常')}</b>,
            content: (
              <div className={styles['receiving-except-message']}>
                {i18n.get(tipsMessage)}
                <div className={styles['header']}>
                  {i18n.get('未交单号：')}
                  <span className={styles['content']}>
                    {(receivingExcepData || []).slice(0, 5).join('、')}
                    {receivingExcepData?.length > 5 && <span>...</span>}
                  </span>
                </div>
                <div>{i18n.get('更多详情您可在【我的】>【我的单据】列表进行查看。')}</div>
              </div>
            ),
            okText: i18n.get('知道了'),
            onOk: () => {
              if (isSubmit) {
                resolve(this.handleSubmit(params, modify))
              } else {
                if (supportBillDetailsSwitchingInDrawer()) {
                  this.props.bus
                    .getValueWithValidate(1)
                    .then((formValue) => {
                      this.handleSaveBill(formValue)
                    }).finally(() => {
                      reject()
                    })
                } else {
                  reject(this.props.bus.invoke('footer:action:save'))
                }

              }
            }
          })
        } else {
          resolve(this.handleSubmit(params, modify))
        }
      } else {
        resolve(this.handleSubmit(params, modify))
      }
    })
  }

  handleSubmit = (params, modify) => {
    const { bus, dataSource, newBillTableList, newFormId, layer } = this.props
    const fnSaveFlowForSubmit = this.fnSaveFlowForSubmit
    // this.fnMoveToRecordExpends()
    const formType = get(dataSource, 'formType')
    const baseType = ['settlement', 'reconciliation']
    if (!baseType.includes(formType)) {
      this.fnMoveToRecordExpends()
    }
    const details = params?.form?.details || []
    return this.checkDetailsFeeTypeDetailId(details).then(res => {
      const endTime = new Date().getTime()
      // TODO: 提交单据埋点
      const specification = this?.state?.currentSpecification
      try {
        let total_attachments_count = 0
        const aiAttachmentsFields = specification?.components?.filter(v => {
          if (v.type === "aiAttachments" && !v.hide) {
            total_attachments_count += params?.form?.[v.field]?.length || 0
            return true
          }
          return false
        })
        const ai_attachments_count = document.querySelectorAll('.attachment .suport-data-ai-summary')?.length || 0
        const ai_fields_applied_count = document.querySelectorAll('[data-ai-icon-wrapper]')?.length || 0
        const ai_used_count = this.aiUsedCount || 0
        const trackParams = {
          form_id: params?.id || newFormId,
          form_template_id: specification?.id,
          form_type: formType,
          form_template_name: specification?.name,
          ai_used_flag: aiAttachmentsFields.length > 0,
          total_fill_duration: endTime - this.startTime,
          form_lifecycle_duration: endTime - (dataSource?.createTime || this.startTime),
          total_attachments_count,
          ai_attachments_count,
          ai_used_count,
          ai_fields_applied_count,
        }
        AIFillFormTracker.trackAIFillFormSubmit(trackParams)
      } catch (error) {
        console.error('AIFillFormTracker.trackAIFillFormSubmit error', error)
      }
      endFlowFormDataSubmitStatistics({
        templateId: specification?.id,
        templateName: specification?.name
      })
      api?.logger?.info?.('单据提交+时间埋点', {
        specificationId: specification?.id,
        specificationName: specification?.name,
        flowId: params?.id,
        code: dataSource?.form?.code,
        form: params?.form,
        state: dataSource?.state,
        useTime: `${(endTime - this.startTime) / 1000}秒`
      })
      return api
        .open('@bills:BillSubmitModal', { bus, params, fnSaveFlowForSubmit, modify, checkBudgetOccupy })
        .then(value => {
          const { state, id, current, saveError, flowState } = value
          if (saveError) {
            return
          }
          const isFailed = state === 'failed'
          current.id = id
          if (isFailed && modify) {
            return state
          }
          related.clearRelatedData()
          if (window.__SUB_PLANNTFORM__ === 'BILLENTRY') {
            current.state = flowState
            bus.emit('list:line:click', { id, state: flowState })
          }
          api.emit('update:bill:list', current)
          // newBillTableList 我的单据表格模式数据，用这个区分是表格模式
          if (newBillTableList.length && !modify) {
            this.__CURRENT_IS_CHANGED = false // 单据提交之后重置修改标识
            // bus.reload()
            // layer.emitCancel.()
            bus.emit('table:row:click', { ...current?.flow, id })
          }
          return value
        })
    })
  }

  handleModifyBill = async (formValue, backLogOwnerId) => {
    const { dataSource, baseDataProperties, userInfo, civilServiceCard } = this.props
    const { currentSpecification, switchTips, isAutoRepayment, timeField } = this.state
    const rules = currentSpecification?.rules || []
    const switcherRules = rules.filter(item => item.code === '11' && item.standard === true)

    const { loanMoney, payMoney, details } = formValue
    const pay = currentSpecification.configs.find(v => v.ability === 'pay')
    const optionalPayeeByZero = pay && pay?.optionalPayeeByZero
    if (optionalPayeeByZero) {
      formValue.optionalPayeeByZero = optionalPayeeByZero
      let warningMsg = this.fnCheckPayeeByZero(formValue)
      if (warningMsg) {
        showMessage.warning(i18n.get(warningMsg))
        return 'failed'
      }
    }
    if (loanMoney) {
      if (loanMoney.standard * 1 < 0) {
        showMessage.error(i18n.get(`支付金额不得小于0`))
        return
      }
    }
    if (payMoney) {
      if (payMoney.standard * 1 < 0) {
        showMessage.error(i18n.get(`支付金额不得小于0`))
        return
      }
    }
    if (timeField && details?.length && details.find(el => el.shouldSaveFeetype)) {
      // 找出所有具有 shouldSaveFeetype 标识的元素及其索引
      const indices = details
        .map((el, index) => (el.shouldSaveFeetype ? index + 1 : null))
        .filter(index => index !== null)

      showMessage.error(i18n.get('币种汇率发生变化，请点击费用明细{_k1}重新保存后提交', { _k1: indices.join('、') }))
      return 'failed'
    }
    if (civilServiceCard) {
      const { CSCImportEnableByBill, CSCImportEnableByDetail } = this.checkCSCImportEnable(formValue)
      if (!CSCImportEnableByBill && !CSCImportEnableByDetail) {
        showMessage.warning(i18n.get('该模板或费用类型不支持导入公务卡数据'))
        return 'failed'
      }
    }
    //判断开关类型的字段，如果设置了只有开启才能提交单据的限制，则给出提出，并且禁止提交
    const errorSwitch = switcherRules.filter(o => !formValue[o.field])
    const errorFields = switcherRules
      .filter(o => !formValue[o.field])
      .map(o => this._getComponentLabelByField(currentSpecification.components, o.field))
      .join(',')
    if (errorSwitch?.length) {
      const errorMessage = switchTips[errorSwitch?.[0]?.field]
      if (switchTips && errorMessage) {
        showMessage.error(errorMessage)
        return
      } else {
        if (errorFields) {
          showMessage.error(i18n.get(`请勾选「{__k0}」才能提交`, { __k0: errorFields }))
          return
        }
      }
    }
    const result = this.preparePayPlanData(formValue, true)
    if (result) {
      return Promise.reject()
    }
    let value = parseSubmitParamAsFormValue(formValue, currentSpecification, dataSource, baseDataProperties)
    // 如果单据详情里存在checkingBillInfo，需要原封不动传给后端
    const checkingBillInfo = dataSource?.form?.checkingBillInfo
    if (checkingBillInfo) {
      value.form.checkingBillInfo = checkingBillInfo
    }

    if (isAutoRepayment) {
      let autoRepayment = await this.fnGetAutoRepaymentResult()
      if (autoRepayment && autoRepayment?.length > 0) {
        let result = await this.checkAutoRepaymentDataComplete(autoRepayment)
        if (result) {
          return
        } else {
          let result1 = await this.checkAutoRepaymentSingleMoney(autoRepayment)
          if (result1) {
            return
          } else {
            let result2 = await this.checkAutoRepaymentMoney(autoRepayment)
            if (result2) {
              return
            } else {
              value.params = { loanManualRepayment: autoRepayment }
            }
          }
        }
      }
    }

    let writtenOff =
      currentSpecification.configs.find(v => v.ability === 'writtenOff') || currentSpecification.type === 'receipt'
    let canWrittenOff = !!writtenOff
    const mustWrittenOff = get(writtenOff, 'mustWrittenOff', false)

    const avatar = userInfo?.staff?.avatar
    const path1 = 'plan.nodes[0].ebotConfig.type'
    const path2 = 'plan.nodes[0].type'
    const isEBot = get(dataSource, path1) === 'costControlCheck' && get(dataSource, path2) === 'ebot'
    const flowId = dataSource && dataSource.id
    this.setLegalEntity(value)
    return api.dispatch(isShowEbotInfo({ flowId, form: value.form })).then(result => {
      const isShowEbot = result && result.value
      return this.fnGetWrittenOffResult(canWrittenOff, false, mustWrittenOff).then(writtenOff => {
        let hasWrittenOff = false
        if (writtenOff && writtenOff.length) {
          value.params = { ...value.params, loanWrittenOff: writtenOff }
          hasWrittenOff = true
        }
        // 多币种核销时，后端无法校验，需要去掉这块的数据
        // const valueParams = writtenOffSummaryForMutiCurrency ? {} : value.params || {}
        // const checkParams = { form: value.form, hasWrittenOff, formType: value.formType, params: valueParams }
        // return api
        //   .invokeService('@bills:check:Supplement:identity', checkParams)
        //   .then(async res => {
        //     const {
        //       value: { needCertificateNo = [] }
        //     } = res
        //     let checkPayeeResult
        //     if (needCertificateNo.length) {
        //       const {
        //         bus,
        //         CHANGJIEPay,
        //         multiplePayeesMode,
        //         userInfo: { permissions = [] }
        //       } = this.props
        //       const isAdmin = permissions.includes('SYS_ADMIN')
        //       checkPayeeResult = await submitBillLogic({
        //         formValue,
        //         CHANGJIEPay,
        //         bus,
        //         isAdmin,
        //         multiplePayeesMode,
        //         needCertificateNo
        //       })
        //     }
        //     if (checkPayeeResult === 'cancel') {
        //       return 'failed'
        //     }
        return api
          .open('@bills:BillInfoModifyReasonModal', { avatar, isEBot, isShowEbotInfo: isShowEbot })
          .then(data => {
            const { editReason } = data
            const { disableInvoiceList } = this.state
            value.params = { ...value.params, editReason, backLogOwnerId }
            disableInvoiceList && (value.params = { ...value.params, invoiceIdToDisableReson: disableInvoiceList })
            let params = { name: 'freeflow.editApproving', ...value }
            const modify = {}
            modify.isShowEBot = isShowEbot
            return this.handleSubmitWrapper(params, modify)
          })
        // })
        // .catch(e => {
        //   e.msg && showMessage.error(e.msg)
        //   return 'failed'
        // })
      })
    })
  }

  _getComponentLabelByField(components, field) {
    let component = components.filter(item => item.field === field)[0]
    if (component) {
      return component.label
    }
    return ''
  }

  getTempNewVersion = () => {
    const { currentSpecification } = this.state
    const originalId = get(currentSpecification, 'originalId.id')
    const { specificationGroupsList } = this.props
    let temp = null
    specificationGroupsList?.forEach(a => {
      a.specifications.forEach(item => {
        if (item.id.startsWith(originalId)) {
          temp = item
        }
      })
    })

    return temp
  }

  getSpecificationGroupsList = () => {
    const {
      specificationGroupsList,
      dataSource: { formType }
    } = this.props
    return (
      specificationGroupsList?.filter(item => {
        let list = item.specifications.filter(o => o.type === formType)
        if (list.length > 0) {
          item.specifications = list
          return item
        }
      }) || []
    )
  }

  getTempFromModal = async () => {
    const isHome3 = api.getState()['@common'].powers.Home3
    let { currentSpecification } = this.state
    const list = this.getSpecificationGroupsList()
    let temp
    if (isHome3) {
      temp = await api.open('@bills:NewChangeTemplateModal', {
        currentSpecification: currentSpecification,
        specificationGroups: list
      })
    } else {
      temp = await api.open('@bills:ChangeTemplateModal', {
        currentSpecification: currentSpecification,
        specificationGroups: list
      })
    }
    return temp
  }

  //切换单据模板
  handleSpecificationChange = async isUpdateVersion => {
    let { currentSpecification, formRiskWarningData, systemGeneration, subsidyGeneration } = this.state
    let {
      dataSource,
      bus,
      baseDataProperties,
      setValidateError,
      remunerationBatchField,
      legalEntityCurrencyPower
    } = this.props
    await this.clearHistoryCurrencyRates()
    const preSpecUseTimeField = !!this.state.timeField
    let { state } = dataSource
    const canChangeTemplate = state === 'draft' ? true : canChangeTemplateFn(dataSource)
    if ((!canChangeTemplate && !isUpdateVersion) || state === 'modify') return
    const temp = isUpdateVersion ? this.getTempNewVersion() : await this.getTempFromModal()
    const oldTmpId = getV(currentSpecification, 'id', '')
    const newTmpId = getV(temp, 'id', '')
    if (!temp || oldTmpId === newTmpId) {
      if (isUpdateVersion) {
        showMessage.error(i18n.get('没有可用的单据模板，请联系系统管理员'))
      }
      return
    }
    setValidateError({ bill: [], detail: [], trip: [] })
    const { data, value } = await checkValue({
      oldTemplate: currentSpecification,
      newTemplate: temp,
      bus,
      dataSource,
      remunerationBatchField
    })
    // 易商卡授权切换模版的时候清空授权
    if (value?.corporateExpenseCardForm?.id) {
      delete value.corporateExpenseCardForm
    }
    // 更新模板找回 变更标志
    const alterFlag = dataSource?.form?.alterFlag
    if (alterFlag) {
      value.alterFlag = alterFlag
    }
    subsidyGeneration = dataSource?.form?.subsidyGeneration || subsidyGeneration
    systemGeneration = dataSource?.form?.systemGeneration || systemGeneration
    // 更新模板找回 补助相关的两个字段
    if (subsidyGeneration) {
      value.subsidyGeneration = subsidyGeneration
    }
    if (systemGeneration) {
      value.systemGeneration = systemGeneration
    }
    const oldTmpIsRemuneration = checkIsRemuneration(currentSpecification)
    const isRemuneration = checkIsRemuneration(data) //酬金申报明细必须显示，切换清空

    if (isRemuneration ^ oldTmpIsRemuneration) {
      delete value.details
    }
    isRemuneration && fixRemunerationSpecification(data)
    this.setFeeDetailOptional(data)
    await fetchAttachmentConfig()
    let template = parseAsMeta(data, baseDataProperties)
    template = fnParseTemplateFields(template)

    checkTemplateField(currentSpecification, temp, legalEntityCurrencyPower, bus)
    dataSource.formType = data.type
    let writtenOffRecords = await this.fnGetWrittenOffResult(false, true)

    // 针对收款的处理
    await handlePayInfoOfSpecifationChange({
      value,
      templateInfo: data,
      saveMultiplePayeed: this.saveMultiplePayeed.bind(this),
      preparePayPlanData: this.preparePayPlanData.bind(this),
    })

    let v = formatNewTemplateValue(value, template, currentSpecification)
    if (!isUpdateVersion) {
      v = formatExpenseLinkInfo(v, temp)
    }
    if (dataSource?.form?.systemGenerationSettle !== undefined) {
      v.systemGenerationSettle = dataSource?.form?.systemGenerationSettle
    }
    updateFormData(dataSource, v)
    // 查询单据模板上所有固定值并重新赋值
    template.forEach(tmp => {
      const tmpValue = constantValue(tmp)
      if (tmpValue) {
        v[tmp.field] = tmpValue
      }
    })
    // 获取报销单手动还款组件的可见性
    const isAutoRepayment = get(
      get(temp, 'configs', []).find(v => v.ability === 'allowRepayment'),
      'isAllowedRepayment',
      false
    )
    this.setState({ template: null }, async () => {
      this.fnGetApplyByExpense(data, value.expenseLink || value.expenseLinks)
      if (v.linkRequisitionInfo) {
        //清空不同申请单类型的申请事项
        var oldRequisition = null
        if (typeof v.linkRequisitionInfo.specificationId === 'object') {
          oldRequisition = v.linkRequisitionInfo.specificationId.configs.filter(v => v.ability === 'requisition')[0]
            .applyContentRule
        }
        const config = data.configs.filter(v => v.ability === 'requisition')[0]
        const newRequisition = config && config.applyContentRule
        if (oldRequisition !== newRequisition) {
          v.linkRequisitionInfo = null
        }
      }

      if (this.expenseLink) {
        let submitterComponent = template.find(v => v.name === 'submitterId')
        if (submitterComponent) {
          submitterComponent.editable = false
        }
      }
      let isHiddenRiskWarning = false // !!~['modify'].indexOf(dataSource.state)
      const extraRiskWarningList = getExtraRiskWarningList(template, formRiskWarningData)
      const validRiskWarningData = getValidRiskWarningData(template, formRiskWarningData)
      const riskInTemplateList = getRiskInTemplate(template, validRiskWarningData)
      const riskFieldNum = isHiddenRiskWarning ? 0 : getRiskFieldNum(validRiskWarningData)

      //按票审阅-》非按票审阅 清空核发金额
      !canModifyApproveMoney(data) &&
        v?.details?.map(fee => {
          const invoices = get(fee, 'feeTypeForm.invoiceForm.invoices', [])
          invoices?.map(item => {
            delete item.approveAmount
            delete item.comment
          })
        })
      const oldIsEBussCard = currentSpecification.configs.find(item => item.isEBussCard)?.isEBussCard
      const newIsEBussCard = temp.configs.find(item => item.isEBussCard)?.isEBussCard
      if (oldIsEBussCard !== newIsEBussCard) {
        // 移入随手记的费用
        this.RecordExpendsDetails = v?.details
        delete v?.details
        writtenOffRecords = []
      }

      if (value?.legalEntityMultiCurrency && !v?.legalEntityMultiCurrency && writtenOffRecords.length) {
        writtenOffRecords = []
      }
      // const timeField = await this.getTimeField(data)
      const [timeField, hiddenFields] = await Promise.all([this.getTimeField(data), getSpecificationHiddenFields(data)])
      this.setState(
        {
          hiddenFields: hiddenFields ?? [],
          template,
          currentSpecification: data,
          value: v,
          writtenOffRecords,
          extraRiskWarningList,
          formRiskWarningData: validRiskWarningData,
          riskFieldNum,
          riskInTemplateList,
          isAutoRepayment,
          timeField
        },
        async () => {
          const { isQuickExpends } = this.state
          dataSource.form = v ? { ...v } : v
          dataSource.form.specificationId = temp
          if (isQuickExpends) {
            // 快速报销单据切换模板清空费用明细
            dataSource.form.details = []
          }

          const travelPlanning = dataSource?.form?.travelPlanning
          const hadScene = travelPlanning?.filter(v => v?.sceneList?.length)?.length
          const tripDataLink = dataSource?.form?.['u_行程规划']
          const hadTripDataLinkScene = tripDataLink?.filter(v => {
            const dataLinkForm = v?.dataLinkForm || {}
            const key = Object.keys(dataLinkForm).find(key => key?.includes('_场景'))
            return dataLinkForm[key]
          })?.length
          if (hadScene || hadTripDataLinkScene) {
            message.warning('申请单模板已切换，请重新填写行程中的场景字段')
          }

          api.invoke('bill:specification:change', { dataSource })
          this.fnGetCalculateField(value.submitterId, false, false, true)
          !isHiddenRiskWarning && bus?.setFieldsExternalsData?.({ ...validRiskWarningData })
          if (this.props.AutoGenerateFeeDetail) {
            await this.getAutoGenerationFeeRules(dataSource)
            setTimeout(() => {
              this.fnAutoGenerationFee(null, 'all', this.state.currentSpecification)
            }, 1000)
          }
          // @todo 更新模板
          if (this.props.ExpressCharge) {
            this.getReceiveExceptionBill(dataSource)
          }
          if (timeField) {
            this.handleChangeTimeField({ billData: v })
          } else if (preSpecUseTimeField) {
            this.checkDetailsRates(v)
          }

          api?.logger?.info?.('web端切换模板', {
            specificationId: newTmpId?.id,
            specificationName: currentSpecification?.name,
            specificationOld: currentSpecification,
            specificationNew: temp,
            flowId: dataSource?.id,
            code: dataSource?.form?.code,
            formOld: value,
            formNew: v
          })
        }
      )
      if (['new', 'draft', 'rejected'].includes(dataSource.state)) {
        this.fnCheckDelegater(value.submitterId, data, true)
      }
    })
    this._enableDraftConfig(this.props)
  }

  fnClearPayeeId = async submitterId => {
    const { bus, multiplePayeesMode } = this.props
    let { currentSpecification } = this.state
    const formType = currentSpecification?.type
    if (multiplePayeesMode) {
      const result = await bus.getFieldsValue()
      let details = result?.details || []
      details = details.map(v => {
        if (v?.feeTypeForm?.feeDetailPayeeId) {
          v.feeTypeForm.feeDetailPayeeId = {}
        }
        return v
      })
      bus.setFieldsValue({ details })
      this.PayPlanStore?.clearObjs()
    } else {
      const res = await api.invokeService('@bills:get:default:payee', {
        formType,
        submitterId
      })
      bus.setFieldsValue({ payeeId: res?.value || {} })
    }
  }

  fnCheckDelegater = async (submitterId, currentSpecification, isChange = false) => {
    const { userInfo, bus } = this.props
    if (submitterId.id === userInfo.staff.id) return
    const delegatorList = await api.dataLoader('@common.delegators').load()
    const canDelegatorList = delegatorList?.filter(de => {
      return (
        de.id == submitterId.id &&
        de.delegateType === currentSpecification?.type &&
        (!de.specIds ||
          de.specIds.length === 0 ||
          de.specIds.indexOf(currentSpecification?.originalId?.id || currentSpecification?.originalId) > -1)
      )
    })
    if (!canDelegatorList || canDelegatorList?.length == 0) {
      showModal.info({
        content: i18n.get('模板不支持当前委托关系，将变更提交人'),
        okText: i18n.get('确定'),
        onOk: () => {
          bus.emit('on:submitterId:change', userInfo.staff, submitterId, { fieldName: 'submitterId' })
          api.invokeService('@bills:set:submitter:data', userInfo.staff)
          this.fnClearPayeeId(userInfo?.staff?.id)
        }
      })
    }
    // 非本账号提交人，切换模板就清空申请单选中的数据项
    if (isChange && submitterId.id !== userInfo.staff.id) {
      bus.setFieldsValue({ expenseLink: null, expenseLinks: null, linkRequisitionInfo: null })
    }
    this.fnGetApplyByExpense(currentSpecification)
  }

  resetExternalFn = (params, formRiskWarnings, isHiddenRiskWarning) => {
    const { bus } = this.props
    this.setState({ ...params }, () => {
      !isHiddenRiskWarning && bus?.setFieldsExternalsData?.({ ...formRiskWarnings })
    })
  }

  resetFieldsExternalsData = data => {
    resetFieldsExternalsForBillInfoEditable(data, this.props, this.state, this.resetExternalFn)
  }

  delDetailsExternalsData = (delDetailIds = []) => {
    delDetailsExternalsForBillInfoEditable(delDetailIds, this.state, this.resetExternalFn)
  }

  _handlerCurrencyMoneySelectChange = data => {
    const { dataSource, canUseDefaultCurrency } = this.props
    canUseDefaultCurrency && dataSource.state === 'new' && handlerCurrencyMoneySelectChange.call(this, data, 'billInfo')
  }

  _handleDimentionCurrencyChange = (data, isCurrencyChange) => {
    handleDimentionCurrencyChange.call(this, data)
    // 根据时间字段获取对应版本的汇率
    this.handleChangeTimeField({ currencyNumCode: data?.currency?.numCode })
    this.showLegalEntityChangedModal(isCurrencyChange)

  }

  showLegalEntityChangedModal = debounce(async (isCurrencyChange) => {
    const { details, legalEntityMultiCurrency } = await this.props.bus.getValue()
    // 币种不一致
    if(legalEntityMultiCurrency && details?.length && enableOtherInvoiceByDimension() && isCurrencyChange){
      const isHasOtherInvoice = details.some(item =>
        item.feeTypeForm?.invoiceForm?.invoices?.some(invoice =>
          !['system_发票主体', 'system_海外票据'].includes(invoice?.master?.entityId)
        )
      )
      if (isHasOtherInvoice) {
        showModal.info({
          title: i18n.get('更新提示'),
          content: i18n.get('法人实体已变更，请到费用明细中的发票编辑页面重新保存发票。'),
          okText: i18n.get('确定')
        })
      }
    }
  }, 1000)

  onOpenOwnerLoanList = () => {
    let { dataSource, privilegeId, showAllFeeType } = this.props
    api.invokeService('@audit:get:loan:Details', dataSource, privilegeId, showAllFeeType)
  }

  reportPerformanceTime = once(() => {
    const { dataSource } = this.props
    const { currentSpecification } = this.props
    endOpenFlowPerformanceStatistics({ flowId: dataSource?.id, specification: currentSpecification })
  })

  getNodesAIAgentMap = async () => {
    const { dataSource } = this.props
    const nodes = getFlowPlanFromLogs(dataSource?.plan, dataSource?.logs)?.nodes || []
    const map = await fetchNodesAIAgentMap({}, nodes, dataSource?.logs)
    this.setState({ nodesAIAgentMap: map })
  }

  render() {
    let {
      bus,
      dataSource,
      userInfo,
      baseDataProperties,
      changeTabAction,
      isModify,
      lastChoice,
      approveModify,
      currentNode,
      feeDetailUneditable,
      multiplePayeesMode,
      payPlanMode,
      validateError = {},
      inModal
    } = this.props
    let {
      hiddenFields,
      currentSpecification,
      template,
      writtenOffRecords,
      value,
      autoCalFields,
      customizeQueryRule,
      submitterId,
      riskInTemplateList,
      extraRiskWarningList,
      riskFieldNum,
      errorMsg,
      showAllFeeType,
      feeTypeVisibleObjForModify,
      applicationListDetails,
      riskData,
      riskInfo,
      isAutoRepayment,
      isQuickExpends,
      repaymentRecords,
      timeField
    } = this.state
    if (!template || !dataSource || !currentSpecification || !userInfo) {
      return <div className="dis fd-c flex-1" />
    }
    this.reportPerformanceTime()
    // 判断是否是从快速报销发起的单据,在单据明细组件中使用，判断按钮显示
    if (isQuickExpends) {
      const detailsItem = template.find(el => el?.field === 'details')
      if (detailsItem) {
        detailsItem.isQuickExpends = isQuickExpends
      }
    }

    let canWrittenOff = !!currentSpecification.configs.find(v => v.ability === 'writtenOff' && !v.writeOffTurnOff)
    let isReceiptTemplate = !!currentSpecification.configs.find(v => v.ability === 'receipt')
    let receiptMoneyAbility = !!currentSpecification.configs.find(
      v => v.ability === 'receipt' && v.summarySelect === summarySelectType.receipt
    )
    let paybackMoneyAbility = !!currentSpecification.configs.find(
      v => v.ability === 'receipt' && v.summarySelect === summarySelectType.repay
    )
    let showMoneyLine = !!currentSpecification.configs.find(v => v.ability === 'pay')
    if (
      (currentSpecification.type === 'requisition' && !showMoneyLine) ||
      currentSpecification.type === 'custom' ||
      currentSpecification.type === 'receipt'
    ) {
      showMoneyLine = !!currentSpecification.components.find(v => v.field === 'details')
    }
    const { state, ownerId, logs = [] } = dataSource
    const submitNodes = logs.filter(v => v.action === 'freeflow.submit').map(v => v.attributes)
    const { sensitiveContent, sensitiveAttachment } = submitNodes[submitNodes.length - 1] || {}
    const tags = {
      submitterId: { isModify },
      details: { initValue: get(dataSource, 'form.details', []), dataSource },
      expenseLink: { writtenOffRecords, fromCreateBill: !!this.expenseLink }
    }
    const notRetract = logs[logs.length - 1]?.action != 'freeflow.retract'
    const isForbid = riskData && riskData.isForbid && riskFieldNum > 0
    // 如果驳回方式选择是从当前节点开始审批时，模板时不能切换的
    const isReconciliation = currentSpecification.type === 'reconciliation' // 对账单
    const isSettlement = currentSpecification.type === 'settlement' // 结算单
    const iscontractWriteEdit =
      currentSpecification.type === 'contractWrite' && (dataSource?.id || dataSource?.form?.contractCode) // 合同录入单，已经存到后台的话，后期都不允许修改单据模版,合同变更也不允许修改，有contractCode表示变更合同
    const iscorpPaymentEdit = currentSpecification.type === 'corpPayment' && (state === 'draft' || state === 'rejected') // 对公付款单且草稿
    const canChangeTemplate =
      !isSettlement &&
      !isReconciliation &&
      !iscontractWriteEdit &&
      !iscorpPaymentEdit &&
      !bus.$_fromPlanQuery &&
      (state === 'draft' ? true : canChangeTemplateFn(dataSource))
    const isOldVersion = currentSpecification && !currentSpecification.active
    // 获取有可见性借款包所属单据模版的模版id列表
    const scopeIds = get(
      get(currentSpecification, 'configs', []).find(v => v.ability === 'writtenOff'),
      'loanList.ids',
      null
    )
    const showSensitive = showSensitiveContent(dataSource, userInfo.staff.id) && STATE_LIST.indexOf(state) < 0
    const isModifyPayPlan = state === 'draft' || state === 'new' || state === 'rejected'
    const isNotPayPlan = !isAllowModifyFiled(currentNode, 'payPlan')
    //审批中修改 并且是按明细
    const isModifyByPayPlanDetails = payPlanMode === false && (isModify || !isModify)
    const isApproveModify = isModifyByPayPlanDetails ? false : isModify
    const isEnabledPayPlan = isNotPayPlan ? false : isApproveModify
    const alterFlag = dataSource?.form?.alterFlag >= '1'
    const showPayPlan = dataSource?.formType === 'expense' && multiplePayeesMode

    // 支付金额不为0时收款信息必填
    const pay = currentSpecification.configs.find(v => v.ability === 'pay')
    const targetTotalMoney = Number(this.PayPlanStore?.targetTotalMoney || 0)
    if (pay && pay?.optionalPayeeByZero) {
      const field = template.find(item => item.name === 'payeeId')
      if (field) {
        field.optionalPayeeByZero = true
        field.optional = dataSource.formType === 'expense' ? !targetTotalMoney : !this.loanMoney
        field.allowClear = true
      }
    }

    const { groupTemplate, isGroup } = splitTemplateToGroups(template)
    const isPermitForm = dataSource?.openFrom === 'permit-form' // flow-preview: 审批流预览 permit-form: 商城预置单填单
    const IconCompo = getSpecificationIconByName(currentSpecification.icon)
    const name = getSpecificationName(currentSpecification)
    return (
      <div
        key={dataSource?.id}
        id="bill-info-editable-container"
        className={classnames(styles['bill-info-editable'], {
          'highlight-modifiable-field': currentNode?.config?.highlightModifiableField || false
        })}
      >
        <div className={classnames('content-wrap')}>
          <div className={classnames('layout5-content')}>
            {!isPermitForm && (
              <div className="group-item">
                <div
                  className="template"
                  style={'modify' === state ? { pointerEvents: 'none', opacity: 0.4 } : undefined}
                >
                  <IconCompo className="icon icon-my" style={{ color: currentSpecification.color }} fontSize={18} />
                  <div className="flex-1 template-name">{isOldVersion ? name + i18n.get('(旧)') : name}</div>
                  {isOldVersion && (
                    <Button
                      category="text"
                      theme="highlight"
                      className="mr-8"
                      size="small"
                      onClick={() => this.handleSpecificationChange(true)}
                    >
                      {i18n.get('更新')}
                    </Button>
                  )}
                  {!alterFlag && canChangeTemplate ? (
                    <Button
                      category="text"
                      theme="highlight"
                      size="small"
                      onClick={() => this.handleSpecificationChange()}
                    >
                      {i18n.get('切换')}
                    </Button>
                  ) : null}
                </div>
                {currentSpecification?.mustUpdateTemplate && !isModify && isOldVersion && (
                  <UpdateTemplate handleSpecificationChange={this.handleSpecificationChange} />
                )}
                {showSensitive && <WarningSensitive content={sensitiveContent} attachments={sensitiveAttachment} />}
                {riskFieldNum > 0 && (
                  <WarningOrErrorTips
                    riskInTemplateList={riskInTemplateList}
                    extraRiskWarningList={extraRiskWarningList}
                    onOpenOwnerLoanList={this.onOpenOwnerLoanList}
                    isForbid={isForbid}
                    riskFieldNum={riskFieldNum}
                  />
                )}
                {!isModify && notRetract && (
                  <LogsCardView
                    bus={bus}
                    dataSource={dataSource}
                    changeTab={changeTabAction}
                    userInfo={userInfo.staff}
                    nodesAIAgentMap={this.state.nodesAIAgentMap}
                  />
                )}
              </div>
            )}
            <div className={classnames({ 'group-item': !isGroup })}>
              <DynamicEditable
                hiddenFields={hiddenFields}
                isCopy={dataSource.isCopy}
                autoCalFields={autoCalFields}
                customizeQueryRule={customizeQueryRule}
                flowRulePerformLogs={dataSource.flowRulePerformLogs}
                bus={bus}
                template={template}
                groupTemplate={groupTemplate}
                value={{ ...(dataSource?.flowValue || {}), ...value }}
                tags={tags}
                submitterId={submitterId}
                ownerId={ownerId}
                billSpecification={currentSpecification}
                baseDataProperties={baseDataProperties}
                approveModify={approveModify}
                isModify={isModify}
                flowId={dataSource && dataSource.id}
                lastChoice={lastChoice}
                currentNode={currentNode}
                billState={dataSource.state}
                isForbid={isForbid}
                showAllFeeType={showAllFeeType}
                feeTypeVisibleObjForModify={feeTypeVisibleObjForModify}
                expenseLink={value.expenseLink}
                feeDetailUneditable={feeDetailUneditable}
                validateError={validateError.bill}
                applicationListDetails={applicationListDetails}
                originalValue={dataSource.form}
                logs={dataSource && dataSource.logs}
                riskData={riskData}
                riskInfo={riskInfo}
                alterFlag={alterFlag}
                PayPlanStore={this.PayPlanStore}
                classNameGroup="group-item"
                inModal={inModal}
                dataSource={dataSource}
                timeField={timeField}
                dataFromOrder={dataSource?.dataFromOrder}
                isPermitForm={isPermitForm}
                useEUI
                businessType={'FLOW'}
              />
            </div>
            {!isPermitForm && showPayPlan && (
              <div className="group-item">
                <Provider PayPlanStore={this.PayPlanStore}>
                  <PayPlanWrapper
                    className="mb-24"
                    errorMsg={errorMsg}
                    baseDataProperties={baseDataProperties}
                    editable={!!(isModifyPayPlan || isModify || isModifyPayPlan(this.props, 'payPlan'))}
                    isModify={isEnabledPayPlan}
                    value={value}
                    flowId={dataSource && dataSource.id}
                    bus={bus}
                    dataSource={dataSource}
                    canPay={!!currentSpecification.configs.find(v => v.ability === 'pay')}
                    billSpecification={currentSpecification}
                  />
                </Provider>
              </div>
            )}
            {!isPermitForm && (canWrittenOff || isReceiptTemplate) && (
              <div className="group-item">
                <WrittenOffPart
                  value={writtenOffRecords}
                  baseDataProperties={baseDataProperties}
                  details={value.details}
                  bus={bus}
                  dataSource={dataSource}
                  flowId={dataSource.id}
                  submitter={submitterId}
                  initValue={dataSource.writtenOff ? dataSource.writtenOff.records : []} //为了添加核销的时候能够正确计算最多可核销值,保存一份初始值
                  isModify={isModify}
                  scopeIds={scopeIds}
                  expenseLink={value.expenseLink || value.expenseLinks}
                  billSpecification={currentSpecification}
                  isReceiptTemplate={isReceiptTemplate}
                />
              </div>
            )}
            {!isPermitForm && isAutoRepayment && !isModify && (
              <div className="group-item">
                <AutoRepaymentPart
                  bus={bus}
                  currentSpecification={currentSpecification}
                  value={repaymentRecords}
                  writtenOff={writtenOffRecords}
                />
              </div>
            )}
            {!isPermitForm && repaymentRecords?.length && isModify ? (
              <div className="group-item">
                <AutoRepaymentPartReadOnly isEdit={false} value={repaymentRecords} writtenOff={writtenOffRecords} />
              </div>
            ) : (
              <div />
            )}
            {!isPermitForm && showMoneyLine && (
              <RightBottomLine
                bus={bus}
                value={value}
                isNotPayPlan={isNotPayPlan}
                dataSource={dataSource}
                writtenOffRecords={canWrittenOff ? writtenOffRecords : []} //不能核销的模板没有核销记录
                canPay={!!currentSpecification.configs.find(v => v.ability === 'pay')}
                isReceiptTemplate={isReceiptTemplate}
                receiptMoneyAbility={receiptMoneyAbility}
                paybackMoneyAbility={paybackMoneyAbility}
                billSpecification={currentSpecification}
                className="group-item"
              />
            )}
          </div>
        </div>
      </div>
    )
  }

  $startAutoCalc2RequestTime = Date.now()

  startAutoCalc2 = async value => {
    let data = cloneDeep(value)
    await new Promise(r => setTimeout(r, 300))
    const requestTime = Date.now()
    this.$startAutoCalc2RequestTime = requestTime
    const { baseDataProperties, baseDataPropertiesMap, bus } = this.props
    const { submitterId, currentSpecification } = this.state
    if (!submitterId) return
    const billData = cloneDeep(await bus.getValue())
    billData?.details?.forEach(detail => {
      if (!detail?.feeTypeForm?.detailId) {
        let tempDataDetail = data.find(i => i.idx === detail.idx)
        if (tempDataDetail?.feeTypeForm?.detailId) {
          detail.feeTypeForm.detailId = tempDataDetail?.feeTypeForm?.detailId
        }
      }
    })
    data = billData.details ?? data
    try {
      bus.emit('savebtn:state:change', { disabled: true })
      // 计算接口
      const calcResult = await AutoCalculate2.calc(
        {
          ...billData,
          specificationId: currentSpecification
        },
        baseDataProperties
      )
      const effectDetailIdList = uniq(calcResult.map(item => item.detailId))
      if (effectDetailIdList.length === 0) return
      data.forEach(detail => {
        const rules = calcResult.filter(v => v.detailId === detail.feeTypeForm.detailId)
        // 由于只有数字型的赋值，不需要考虑其他情况
        rules.forEach(({ fieldName, fieldValue }) => {
          detail.feeTypeForm[fieldName] = fieldValue.toString()
        })
      })

      // 重新进行系统计算
      const effectDetailList = data.filter(v => effectDetailIdList.includes(v.feeTypeForm.detailId))
      const systemCalc = async detail => {
        const { feeTypeForm, feeTypeId: feeType, specificationId: specification } = detail
        const _billData = {
          details: {},
          submitterId,
          specificationId: currentSpecification
        }
        const formData = {
          feeTypeForm,
          specificationId: specification.id,
          feeTypeId: feeType.id
        }

        _billData.details = [formData]

        const template = parseAsMeta(specification, baseDataProperties)
        return await AutoCalculate2.getAutoCal1ResultValue(
          _billData,
          formData,
          currentSpecification,
          baseDataProperties,
          baseDataPropertiesMap,
          submitterId.id,
          template
        )
      }

      const calc1ActionList = await Promise.all(
        effectDetailList.map(async detail => {
          const result = await systemCalc(detail)
          return {
            detail,
            result
          }
        })
      )
      const resultBillData = await bus.getValue()
      if (requestTime !== this.$startAutoCalc2RequestTime) return console.log('skip calc 2')

      // 赋值
      const calc1ActionMap = calc1ActionList.reduce(
        (result, { detail, result: v }) => ({ ...result, [detail.feeTypeForm.detailId]: v }),
        {}
      )
      resultBillData.details.forEach(detail => {
        const rules = calcResult.filter(v => v.detailId === detail.feeTypeForm.detailId)
        const calc1Result = calc1ActionMap[detail.feeTypeForm.detailId]
        // 由于只有数字型的赋值，不需要考虑其他情况
        rules.forEach(({ detailId, fieldName, fieldValue }) => {
          if (detail.feeTypeForm.detailId == detailId) {
            detail.feeTypeForm[fieldName] = fieldValue.toString()
          }
        })
        if (calc1Result) {
          // 系统计算赋值
          Object.keys(calc1Result).forEach(key => {
            detail.feeTypeForm[key] = calc1Result[key]
          })
        }
      })

      bus.setFieldsValue({ details: resultBillData.details })
    } finally {
      bus.emit('savebtn:state:change', { disabled: false })
    }
  }
}
