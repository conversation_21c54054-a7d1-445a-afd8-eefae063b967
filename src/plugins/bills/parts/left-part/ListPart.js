/**************************************************
 * Created by nany<PERSON><PERSON>feng on 10/07/2017 17:41.
 **************************************************/
import styles from './ListPart.module.less'
import React, { PureComponent } from 'react'
import classnames from 'classnames'
import ListBillItem from '../../elements/list-bill-item'
import EmptyBody from '../../elements/EmptyBody'
import { app as api } from '@ekuaibao/whispered'
import { fnApportionDetails, getChangeTemplateModalKey, logCreateBillFromRecordNote } from '../../util/billUtils'
import { Skeleton, Button } from '@hose/eui'

export default class ListPart extends PureComponent {
  state = {
    currentItem: void 0
  }

  componentDidMount() {
    const { bus, openTemplateModalDeta } = this.props
    if (!bus) return
    bus.on('list:select:change', this.handleLineClick)
    api.watch('from:requisition', this.handleFromRequisition)
    if (openTemplateModalDeta.isOpen) {
      const { requisitionInfo, isApply } = openTemplateModalDeta
      this.handleFromRequisition(requisitionInfo, isApply)
    }
  }

  componentWillReceiveProps(np) {
    if (this.props.openTemplateModalDeta !== np.openTemplateModalDeta && np.openTemplateModalDeta.isOpen) {
      const { openTemplateModalDeta } = np
      const { requisitionInfo, isApply } = openTemplateModalDeta
      this.handleFromRequisition(requisitionInfo, isApply)
    }
  }

  componentWillUnmount() {
    const { bus } = this.props
    if (!bus) return
    bus.un('list:select:change', this.handleLineClick)
    api.un('from:requisition', this.handleFromRequisition)
  }

  handleFromRequisition = async (args, isApply) => {
    const { bus } = this.props
    const modalKey = getChangeTemplateModalKey()
    const data = await api.open(modalKey, {
      specificationGroups: args.visibleSpecificationList
    })
    args.defaultSpecification = data
    // 快速报销和随手记的自动分摊逻辑
    if (args.isQuickExpends || args.withNotes?.length) {
      args.withNotes = await fnApportionDetails(args.withNotes, data)
    }
    if (args.noteIds) {
      logCreateBillFromRecordNote({ data: args.noteIds, billSpecificationId: data.id })
    }
    const callback = () => {
      bus.emit('list:select:change', {
        formType: data.type,
        state: 'new',
        requisitionInfo: isApply ? args : undefined
      })
    }
    bus.invoke('check:value:changed', data.type).then(
      res => {
        callback()
      },
      error => {
        if (error === 'cancel') return
        callback()
      }
    )
  }

  handleLineChange = (line = {}) => {
    let { bus, setValidateError } = this.props
    if (!bus) return
    let { currentItem } = this.state
    if (currentItem === line.id) return
    setValidateError({ bill: [], detail: [], trip: {} })
    try {
      bus.invoke('check:value:changed').then(
        res => {
          this.handleLineClick(line)
        },
        error => {
          if (error === 'cancel') return
          this.handleLineClick(line)
        }
      )
    } catch (e) {}
  }

  handleLineClick = line => {
    let { bus } = this.props
    if (line && line.state !== 'new') {
      this.setState({ currentItem: line.id })
    } else {
      this.setState({ currentItem: void 0 })
    }
    bus.emit('list:line:click', line)
  }

  fnBuildItems(dataSource = []) {
    let { searchKey, loadMore } = this.props
    let { currentItem } = this.state
    return dataSource.map(line => {
      if (line.type === 'loadMore') {
        return (
          <div key="load-more" className="load-more" >
            <Button category='text' theme='highlight' onClick={loadMore}>
              {i18n.get('点击加载更多')}
            </Button>
          </div>
        )
      }
      return (
        <ListBillItem
          key={line.flow.id}
          itemKey={line.flow.id}
          searchKey={searchKey}
          isSelect={currentItem === line.flow.id}
          onClick={line => this.handleLineChange(line.flow)}
          isShowCode={false}
          dataSource={line}
        />
      )
    })
  }

  renderSkeleton = () => {
    return (
      <div className={styles['skeleton-modal']}>
          {Array.from({ length: 11 }).map((_, i) => (
            <div  className='body'>
              <Skeleton type="text" animated/>
            </div>
          ))}
      </div>
    )
  }

  render() {
    const { bills, className, inSearch, isSkeleton } = this.props
    const cls = classnames(styles['bill-list-wrap'], styles['bill-list-wrap-layout5'], className)
    if (isSkeleton) {
      return this.renderSkeleton()

    }
    return (
      <div id="ListPartDataContainer" className={cls}>
        {bills.length ? (
          this.fnBuildItems(bills)
        ) : (
          <EmptyBody
            type="billList"
            label={!inSearch ? i18n.get('您发起的单据将会显示在这里') : i18n.get('没有找到您所要的结果')}
          />
        )}
      </div>
    )
  }
}
