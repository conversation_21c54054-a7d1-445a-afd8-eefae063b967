/**************************************************
 * Created by kaili on 2017/7/20 下午12:54.
 **************************************************/
import React, { Component } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import key from '../../key'
import styles from './ListTab.module.less'
import { app as api } from '@ekuaibao/whispered'
import { QuerySelect } from 'ekbc-query-builder'
import ListPart from './ListPart'
import RightPart from '../right-part/RightPart'
import MybillListTip from '../../elements/MybillListTip'
import BillFilter from '../../elements/BillFilter'
import ListHeader from './ListHeader'
import { confirmTotalFlows, setValidateError, getMy<PERSON>aid<PERSON>ill, getFlowInfoById } from '../../bills.action'
import { formatCopyBillData, confirmCopy, getFilterList } from '../../util/billUtils'
import classnames from 'classnames'
import { getV } from '@ekuaibao/lib/lib/help'
import BillData from './MyBillListData'
import { related } from '../../../../elements/feeDetailViewList/Related'
import { trackBillListLoadTime } from '../../util/trackBill'
import { cloneDeep, get } from 'lodash'
import { showModal } from '@ekuaibao/show-util'
import { Fetch } from '@ekuaibao/fetch'
import ListViewTitle from './Title'
import driveDoneBillsOnBoarding from '../../../../components/guide/driveDoneBillsOnBoarding'
import AIChatEntry from '../../../../components/AIChatEntry'
import { enableHidingFinishedBills } from '../../../../lib/featbit'


const sortLocalStorage = {
  get key() {
    const userId = api.getState('@common')?.userinfo?.staff?.id
    return `Hose-mybill-current-sort-option_${userId}`
  },
  get: () => {
    try {
      const cache = JSON.parse(localStorage.getItem(sortLocalStorage.key))
      return cache || { value: 'form.submitDate', order: 'desc' }
    } catch (error) {
      return null
    }
  },
  set: (value) => {
    localStorage.setItem(sortLocalStorage.key, JSON.stringify(value))
  }
}

@EnhanceConnect(
  state => {
    return {
      openTemplateModalDeta: state[key.ID].openTemplateModalDeta,
      paidList: state[key.ID].paidList,
      balance: state[key.ID].balance,
      printListCount: state[key.ID].printListCount,
      specificationGroups: state['@custom-specification'].specificationGroupsList,
      KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2
    }
  },
  { confirmTotalFlows, setValidateError, getMyPaidBill, getFlowInfoById }
)
export default class TabListPart extends Component {
  static defaultProps = {}


  constructor(props) {
    super(props)
    this.billData = new BillData()
    this.state = {
      inSearch: false,
      filterValue: '',
      searchValue: '',
      filterTypes: null,
      viewType: 'list',
      billList: [],
      isSkeleton: true,
      currentSortValue: sortLocalStorage.get(),
    }
    this.tabListPartRef = React.createRef()
  }

  componentDidMount() {
    const { bus } = this.props
    api.on('update:bill:list', this.handleUpdateBillList)
    api.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
    this.getHeaderData(true)
    bus.on('update:bill:list', this.handleUpdateBillList)
    api.on('update:bill:open:remuneration', this.handleUpdateRemuneration)
    bus.on('copy:bill', this.confirmCopy)
    // this.loadBillList()
    api.on('reload:left:data', this.reload)

    driveDoneBillsOnBoarding(this.tabListPartRef.current)
  }

  componentWillUnmount() {
    let { bus } = this.props
    api.un('update:bill:list', this.handleUpdateBillList)
    bus.un('update:bill:list', this.handleUpdateBillList)
    api.un('update:bill:open:remuneration', this.handleUpdateRemuneration)
    bus.un('copy:bill', this.confirmCopy)
    api.un('reload:left:data', this.reload)
  }

  /**
   * @description 变更的回调刷新
   */
  reload = line => {
    this.getHeaderData(true)
    this.billData.init()
    this.loadBillList()
    this.props.bus.emit('list:select:change', line)

    document.getElementById('ListPartDataContainer').scrollTo({
      left: 0,
      top: 0,
      behavior: 'smooth'
    })
  }

  fnHandleParams({ filterValue, searchValue, filterTypes, currentSortValue }) {
    let formType =
      !filterValue || filterValue === 'all'
        ? undefined
        : `(form.specificationId.specificationGroupId == "${filterValue}")`
    let filters = ''
    let billTypes = []

    const userInfoId = api.getState('@common')?.userinfo?.staff?.id + '-filter' || 'bill-select-filter'
    const entrustFilter = JSON.parse(localStorage.getItem(userInfoId))?.filter(item => item.type === 'entrust')
    const currentvalue = entrustFilter ? entrustFilter[0]?.children[0]?.type : ''
    let delegatedFilter = currentvalue || 'DELEGATE_TO_ME'

    if (searchValue) {
      const useGlobalSearchV2 = window?.PLATFORMINFO?.useGlobalSearchV2
      const isSearchMoney = /^-?\d+(\.\d{0,2})?$/.test(searchValue)
      const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage === 'en-US'
      searchValue = JSON.stringify(searchValue)
      if (lang) {
        filters = `lower(form.title).containsIgnoreCase(lower(${searchValue}))||lower(form.code).containsIgnoreCase(lower(${searchValue}))`
      } else {
        filters = `form.title.containsIgnoreCase(${searchValue})||form.code.containsIgnoreCase(${searchValue})`
      }
      if (this.props.KA_GLOBAL_SEARCH_2) {
        filters +=
          useGlobalSearchV2 && isSearchMoney
            ? `||form.payMoney.standard.containsIgnoreCase(${searchValue})||form.expenseMoney.standard.containsIgnoreCase(${searchValue})`
            : `||form.containsIgnoreCase(${searchValue})`
      }
      filters = `(${filters})`
    }
    if (!!filterTypes?.length) {
      const query = new QuerySelect().filterBy(formType)

      filterTypes.map(el => {
        if (el?.key === 'state') {
          if (!el?.value?.includes('all')) {
            billTypes = el.value
          }
        } else if (el?.key === 'formType') {
          if (enableHidingFinishedBills()) {
            if (!el?.value?.includes('all')) {
              query.filterBy(`formType.in(${el.value.map(t => `"${t}"`).join(',')})`)
              return
            }
          }
          if (!el?.value?.includes('all')) {
            query.filterBy(`formType == "${el.value.join()}"`)
          }
        } else if (el?.key === 'entrust') {
          delegatedFilter = el.value.join()
        }
      })
      formType = query.value().filterBy
    }
    return {
      formType,
      filters,
      billTypes,
      delegatedFilter,
      orderBy: currentSortValue,
    }
  }

  getParams = () => {
    const { filterValue, searchValue, filterTypes, currentSortValue } = this.state
    return this.fnHandleParams({ filterValue, searchValue: searchValue.trim(), filterTypes, currentSortValue })
  }

  handleUpdateBillList = (item = {}, fromFilterChange = false) => {
    this.billData.init()
    const params = this.getParams()
    if (!fromFilterChange) {
      api.invokeService('@layout5:refresh:menu:data')
    }
    this.loadBillList(params, item)
  }
  handleUpdateRemuneration = (params = {}) => {
    const { id, bus, remunerationBatchField, current } = params
    const { getFlowInfoById } = this.props
    getFlowInfoById({ id }).then(action => {
      let dataSource = cloneDeep(action.payload.value)
      const entityInfo = get(dataSource, `form.${remunerationBatchField}`)
      if (entityInfo?.id) {
        api.open('@remuneration:RemunerationDetailPopup', {
          bus,
          dataSource,
          remunerationBatchField,
          title: i18n.get('编辑酬金明细')
        })
        return
      }
      showModal.info({
        title: i18n.get('酬金业务对象创建中'),
        content: i18n.get('酬金业务对象创建中，请刷新页面再试。'),
        okText: i18n.get('确定')
      })
    })
  }
  loadBillList = (params = {}, item) => {
    this.__FIRST_TIME = Date.now()
    return this.billData.getBills(params).then(items => {

      this.setState({ billList: items, isSkeleton: false }, () => {
        this.fnTrackBillListLoadTime()
      })
      if (!!item && Object.keys(item).length > 0) {
        this.listUpdate(items, item)
      }
      api.invokeService('@bills:save:get:my:billList',items)
      api.invokeService('@bills:get:loan:balance')
    })
  }

  fnTrackBillListLoadTime = () => {
    if (!!this.__FIRST_TIME) {
      trackBillListLoadTime({ startTime: this.__FIRST_TIME, endTime: Date.now() })
    }
  }

  getHeaderData = fromComponentDidMount => {
    if (!fromComponentDidMount) {
      api.invokeService('@layout5:refresh:menu:data')
    }
    // 如果去掉已完成单据，不需要请求数据
    if (enableHidingFinishedBills()) {
      return
    }
    this.getMyPaidBill()
  }

  loadMore = () => {
    const params = this.getParams()
    params.isLoadMore = true
    this.loadBillList(params)
  }

  listUpdate = (billList = [], item) => {
    const { bus } = this.props
    if (!billList.length) {
      return bus.emit('list:select:change')
    }
    let line = item
      ? billList.find(v => {
          const path = 'flow.id'
          const id = getV(v, path, '')
          return id === item.id
        })
      : undefined
    line = window.isNewHome && !!line ? line.flow : line
    return bus.emit('list:select:change', line)
  }

  getMyPaidBill = () => {
    this.props.getMyPaidBill()
  }

  handleFilterChange = ({ filterValue, filterTypes }) => {
    this.setState({ filterValue, filterTypes }, () => {
      this.handleUpdateBillList(undefined, true)
    })
  }

  handleSearchTextChange = ({ filterValue, searchValue, inSearch }) => {
    this.setState({ filterValue, searchValue, inSearch }, () => {
      this.handleUpdateBillList(undefined, true)
    })
  }

  handleFooterClick = () => {
    api.open('@bills:ArchivedStackerModal', {
      viewKey: 'ArchivedTableView',
      type: 'Archived',
      handleCopyBill: this.handleCopyBill
    })
  }

  handleMessageTipClick = params => {
    api.open('@bills:ArchivedStackerModal', {
      viewKey: 'MessageListView',
      ...params,
      updateList: this.getHeaderData.bind(this),
      handleCopyBill: this.handleCopyBill
    })
  }

  confirmCopy = dataSource => {
    related.clearRelatedData()
    confirmCopy(dataSource).then(_ => {
      this.handleCopyBill(dataSource)
    })
  }

  handleCopyBill = async dataSource => {
    const { bus } = this.props
    const data = await formatCopyBillData(dataSource)
    bus.emit('list:select:change', data)
  }

  handleCheckArchived = () => {
    this.handleFooterClick()
    api.store.dispatch('@layout5/setSegmentActiveKey')('Archived')
  }

  handleCheckConfirm = () => {
    this.handleMessageTipClick({
      type: 'paidList',
      copyAble: true
    })
    api.store.dispatch('@layout5/setSegmentActiveKey')('paidList')
  }

  handleConfirmAll = () => {
    const { paidList, confirmTotalFlows } = this.props
    const ids = paidList.map(item => item.id)
    confirmTotalFlows(ids).then(_ => {
      this.getHeaderData()
    })
  }

  handleSortChange = (currentSortValue) => {
    this.setState({ currentSortValue }, () => {
      sortLocalStorage.set(currentSortValue)
      this.handleUpdateBillList(undefined, true)
    })
  }

  renderHeader = () => {
    const { paidList, bus, changeViewType, KA_GLOBAL_SEARCH_2, specificationGroups, currentViewType, ifCanCreate } = this.props
    let { searchValue, inSearch, filterValue, filterTypes, currentSortValue } = this.state
    return (
      <>
        <ListViewTitle
          currentViewType={currentViewType}
          onViewTypeChange={changeViewType}
        />
        <ListHeader
          bus={bus}
          enableGlobalSearch={KA_GLOBAL_SEARCH_2}
          dataSource={{ filterValue, searchValue, inSearch, specificationGroups }}
          onChange={this.handleSearchTextChange}
          changeViewType={changeViewType}
          ifCanCreate={ifCanCreate}
        />
        <MybillListTip
          paidList={paidList}
          checkArchived={this.handleCheckArchived}
          checkConfirm={this.handleCheckConfirm}
          confirmAll={this.handleConfirmAll}
        />
        <AIChatEntry style={{margin: '0 16px 12px'}}/>
        <BillFilter
          bus={bus}
          enableMultiSelect={enableHidingFinishedBills()}
          filterList={getFilterList()}
          dataSource={{ filterValue, searchValue, inSearch, specificationGroups, filterTypes }}
          onChange={this.handleFilterChange}
          currentSortValue={currentSortValue}
          onSortChange={this.handleSortChange}
        />
      </>
    )
  }

  render() {
    const { bus, setValidateError, openTemplateModalDeta } = this.props
    const { searchValue, inSearch, billList, isSkeleton } = this.state
    const wrapperCls = classnames(styles['tab-list-part'], styles['tab-list-part-layout5'])
    return (
      <div className={wrapperCls} ref={this.tabListPartRef}>
        {this.renderHeader()}
        <ListPart
          setValidateError={setValidateError}
          searchKey={searchValue}
          inSearch={inSearch}
          bills={billList}
          isSkeleton={isSkeleton}
          bus={bus}
          loadMore={this.loadMore}
          openTemplateModalDeta={openTemplateModalDeta}
        />
      </div>
    )
  }
}
