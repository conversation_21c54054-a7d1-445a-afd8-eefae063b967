/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/7/13.
 */
@import "~@ekuaibao/eui-styles/less/token";
@import '~@ekuaibao/web-theme-variables/styles/colors';

.bill-list-wrap {
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: @color-white-1;
  padding: 52px 0;

  :global {
    .empty {
      font-size: 14px;
      margin-top: 120px;
    }
    .load-more {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 50px;
    }
  }
}

.bill-list-wrap-layout5{
  margin-left: 16px;
  padding: 0 16px 0 0;
}

.skeleton-modal{
  padding: 16px;
  :global{
    .head{
      height: 24px;
      width: 114px;
      margin-bottom: 24px;
      .eui-skeleton{
        width: 100%;
        height: 100%;
      }
    }
    .body{
      width: 100%;
      height: 16px;
      margin-bottom: 16px;
      .eui-skeleton{
        width: 100%;
        height: 100%;
      }
    }
  }

}
