import React, { useState, useEffect, useMemo } from 'react'
import { Dropdown, Menu, Popover, Button, MenuProps as EUIMenuProps } from '@hose/eui'
import { FilledDirectionExpandDown, FilledDirectionExpandUp, OutlinedDirectionSwitchVertical, OutlinedEditFilter } from '@hose/eui-icons'
import { cloneDeep } from 'lodash'
import classNames from 'classnames'
import EKBIcon from '../../../elements/ekbIcon'
import styles from './BillFilter.module.less'
import { app as api } from '@ekuaibao/whispered'
import MultiSelectFilterGroup, { getCachedMultiSelectFilter, MultiSelectFilterState } from './BillFilterMultiSelect'

const sortMenuConfig = [
  {
    label: i18n.get('按提交时间'),
    key: 'form.submitDate',
    children: [
      {
        label: i18n.get('按最新排序'),
        key: 'form.submitDate-desc',
      },
      {
        label: i18n.get('按最早排序'),
        key: 'form.submitDate-asc',
      }
    ]
  },
  {
    label: i18n.get('按审批状态'),
    key: 'state-asc',
  }
]

interface menuProps {
  type: string
  label: string
  children?: menuProps[]
}

interface FilterProps {
	menuList: menuProps[]
	selectFilter:  menuProps[]
  defaultActiveTypes: menuProps[]
	onChange: (param: menuProps[]) => {}
}

const FilterGroup: React.FC<FilterProps> = (props: FilterProps) => {
	const { menuList, defaultActiveTypes, selectFilter, onChange } = props
	const [selectTypes, setSelectTypes] = useState(defaultActiveTypes)
	const userInfoId = api.getState('@common')?.userinfo?.staff?.id + '-filter' || 'bill-select-filter'

	useEffect(() => {
		setSelectTypes(selectFilter)
	}, [selectFilter])

  const handleClickMenu = (menuType: string, subMenuType: string) => {
    const newSelectTypes = cloneDeep(selectTypes)
    const selectMenu = newSelectTypes.find(el => el.type === menuType)
    const subMenu = menuList.find(el => el.type === menuType)
    const selectSubMenu = subMenu.children.find(el => el.type === subMenuType)
    selectMenu.children = [cloneDeep(selectSubMenu)]
    setSelectTypes(newSelectTypes)
  }
  const handleReset = () => {
    setSelectTypes(defaultActiveTypes)
    localStorage.removeItem(userInfoId)
  }
  const handleOK = () => {
    onChange(selectTypes)
    const cacheSelectFilter = removeEntrustFilterCache(cloneDeep(selectTypes))
    localStorage.setItem(userInfoId, JSON.stringify(cacheSelectFilter))
  }
  return (
    <div className={styles['filter-group-wrapper']}>
      <div className="filter-group-content">
        {menuList.map(menu => {
          if (!menu?.children?.length) {
            return null
          }
          const selectItems = selectTypes.find(el => el.type === menu.type)?.children
          const subMenu = menu.children.map(el => {
            const isSelected = !!selectItems?.filter(item => item.type === el.type).length
            const itemClassName = isSelected ? 'ekb-drop-down-item selected' : 'ekb-drop-down-item'
            return (
              <div key={el.type} className={itemClassName} onClick={() => handleClickMenu(menu.type, el.type)}>
                <span>{el.label}</span>
                {isSelected && (
                  <div className="item-selected-tip">
                    <EKBIcon name="#EDico-check-default" />
                  </div>
                )}
              </div>
            )
          })
          return (
            <div className="ekb-drop-down-group">
              <div className="ekb-drop-down-groupName">
                <span>{menu.label}</span>
              </div>
              {subMenu}
            </div>
          )
        })}
      </div>
      <div className="ekb-drop-down-footer">
        <Button category="secondary" className="mr-16" onClick={handleReset}>
          {i18n.get('重置')}
        </Button>
        <Button category="primary" className="primary-btn" onClick={handleOK}>
          {i18n.get('确定')}
        </Button>
      </div>
    </div>
  )
}

interface Props {
	dataSource: any
	filterList: any[]
	onChange: (param: menuProps[]) => {}
  currentSortValue: { value: string, order: string }
  onSortChange: (currentSort: { value: string, order: string }) => {}
  enableMultiSelect?: boolean
}

const BillFilter: React.FC<Props> = (props: Props) => {
  const {
    dataSource,
    filterList,
    onChange,
    onSortChange,
    currentSortValue,
    enableMultiSelect = false,
  } = props
  const { specificationGroups } = dataSource
  const defaultGroup = { key: 'all', label: i18n.get('全部') }
  const defaultActiveTypes = useMemo(() => {
    return cloneDeep(filterList)
      .map((menu: any) => {
        if (!menu?.children?.length) {
          return null
        }
        menu.children = menu.type === 'entrust' ? [menu.children[1]] : [menu.children[0]]
        return menu
      })
      .filter(el => !!el)
  }, [filterList])

  const [selectGroupKey, setSelectGroupKey] = useState(defaultGroup.key)
  const [selectFilter, setSelectFilter] = useState(defaultActiveTypes)
  const [isShowFilter, setIsShowFilter] = useState(false)
  const [isShowGroup, setIsShowGroup] = useState(false)
  const [isSortVisible, setIsSortVisible] = useState(false)

  useEffect(() => {
    if (enableMultiSelect) {
      const cachedMultiSelectFilter = getCachedMultiSelectFilter()
      onChange({ ...dataSource, filterTypes: cachedMultiSelectFilter, filterValue: selectGroupKey })
      return
    }
    const userInfoId = api.getState('@common')?.userinfo?.staff?.id + '-filter' || 'bill-select-filter'
    const preSelectFilter = localStorage.getItem(userInfoId)
    if (preSelectFilter) {
      try {
        const parsedFilter = JSON.parse(preSelectFilter)
        const cacheSelectFilter = handleEntrustFilter(parsedFilter, filterList)
        setSelectFilter(cacheSelectFilter)
        const filterTypes = parsedFilter.map(el => ({
          key: el.type,
          value: el?.children?.map(item => item.type)
        }))
        onChange({ ...dataSource, filterTypes, filterValue: selectGroupKey })
      } catch (error) {
        console.warn('Failed to parse cached filter:', error)
      }
    } else {
      onChange({ ...dataSource, filterValue: selectGroupKey, })
    }
  }, [])

  const handleMenuClick = (e: StringAnyProps) => {
    const { key, item } = e
    setSelectGroupKey(key)
    setIsShowGroup(false)
    const filterTypes = selectFilter.map(el => ({
      key: el.type,
      value: el?.children?.map(item => item.type)
    }))
    onChange({ ...dataSource, filterValue: key, filterTypes })
  }

  const handleFilterGroupChange = (filters: menuProps[]) => {
    setSelectFilter(filters)
    setIsShowFilter(false)
    const filterTypes = filters.map(el => ({
      key: el.type,
      value: el?.children?.map(item => item.type)
    }))
    onChange({ ...dataSource, filterTypes, filterValue: selectGroupKey })
    return filterTypes
  }

  const handleMultiSelectConfirm = (filterTypes: MultiSelectFilterState) => {
    setIsShowFilter(false)
    onChange({ ...dataSource, filterTypes, filterValue: selectGroupKey })
  }

  const menu = (
    <Menu selectedKeys={[selectGroupKey]} onClick={handleMenuClick}>
      <Menu.Item key={defaultGroup.key}>{defaultGroup.label}</Menu.Item>
      {specificationGroups &&
        specificationGroups.length > 0 &&
        specificationGroups.map(item => {
          return <Menu.Item key={item.id}> {i18n.currentLocale === 'en-US' && item.enName ? item.enName : item.name}</Menu.Item>
        })}
    </Menu>
  )
  const content = enableMultiSelect ? (
    <MultiSelectFilterGroup
      filterList={filterList}
      onConfirm={handleMultiSelectConfirm}
    />
  ) : (
    <FilterGroup
      menuList={filterList}
      selectFilter={selectFilter}
      defaultActiveTypes={defaultActiveTypes}
      onChange={handleFilterGroupChange}
    />
  )
  const selectGroupLabel = useMemo(() => {
    if (selectGroupKey !== defaultGroup.key) {
      return specificationGroups.find(el => el.id === selectGroupKey)?.name
    }
    return defaultGroup.label
  }, [selectGroupKey, specificationGroups])

  const handleSortChange = ({ key }: { key: string }) => {
    const [value, order ] = key.split('-')
    if (value !== currentSortValue.value || order !== currentSortValue.order) {
      onSortChange?.({ value, order })
    }
  }

  const menuOptions: EUIMenuProps = useMemo(() => {
    return {
      items: sortMenuConfig,
      onClick: handleSortChange,
      selectedKeys: [`${currentSortValue.value}-${currentSortValue.order}`]
    }
  }, [handleSortChange, currentSortValue])

  return (
    <div className={styles['bills-filter-wrapper']}>
      <Dropdown
        overlay={menu}
        trigger={['click']}
        visible={isShowGroup}
        onVisibleChange={visible => setIsShowGroup(visible)}
        overlayClassName={styles['bills-filter-dropdown']}
      >
        <div className={classNames('type-name', { 'type-name-open': isShowGroup })}>
          <span className="text-ellipsis type-group-name mr-4">{selectGroupLabel}</span>
          {
            isShowGroup ?<FilledDirectionExpandUp fontSize={12} /> : <FilledDirectionExpandDown fontSize={12} />
          }
        </div>
      </Dropdown>
      <div className={styles['view-options']}>
        <Popover
          content={content}
          placement="bottomRight"
          trigger="click"
          overlayClassName={enableMultiSelect ? styles['filter-popover'] : void 0}
          open={isShowFilter}
          onOpenChange={visible => setIsShowFilter(visible)}
        >
          <Button category='text' className={classNames(styles.action, { [styles['action-open']]: isShowFilter })}>
            {i18n.get('筛选')}
            <OutlinedEditFilter fontSize={12} className={styles.icon} />
          </Button>
        </Popover>
        <Dropdown
          menu={menuOptions}
          trigger={['click']}
          open={isSortVisible}
          onOpenChange={visible => setIsSortVisible(visible)}
        >
          <Button category='text' className={classNames(styles.action, { [styles['action-open']]: isSortVisible })}>
            {i18n.get('排序')}
            <OutlinedDirectionSwitchVertical fontSize={12} className={styles.icon} />
          </Button>
        </Dropdown>
      </div>
    </div>
  )
}

export interface MenuProps {
  type: string
  label: string
  children?: menuProps[]
}


export const handleEntrustFilter = (filterData: MenuProps[] = [], allMenuList: MenuProps[] = []) => {
  if (!Array.isArray(filterData) || !Array.isArray(allMenuList)) {
    return filterData;
  }

  const ENTRUST_TYPE = 'entrust';
  const ALL_DELEGATE = 'ALL_DELEGATE';
  const DEFAULT_CHILD_INDEX = 1;

  // 查找委托类型的菜单
  const entrustMenu = allMenuList.find(item => item.type === ENTRUST_TYPE);
  const filterEntrust = filterData.find(item => item.type === ENTRUST_TYPE);
  if (!filterEntrust) {
    // 如果没有委托类型，添加默认的委托给我选项
    filterData.push({
      ...entrustMenu,
      children: [entrustMenu.children[DEFAULT_CHILD_INDEX]]
    });
    return filterData;
  }

  return filterData.map(line => {
    if (line?.type === ENTRUST_TYPE && Array.isArray(line.children)) {
      const filteredChildren = line.children.filter(item => item.type !== ALL_DELEGATE);
      return {
        ...line,
        children: filteredChildren.length ? filteredChildren : [entrustMenu.children[DEFAULT_CHILD_INDEX]]
      };
    }
    return line;
  });
};

export const removeEntrustFilterCache = (filterData: MenuProps[]) => {
  return filterData.filter(line => {
    return line?.type !== "entrust"
  })
}

export default BillFilter
