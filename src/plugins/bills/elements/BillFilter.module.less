@import '~@ekuaibao/eui-styles/less/token';

.bills-filter-wrapper {
  display: flex;
  justify-content: space-between;
  margin: 0 16px 8px 16px;
  :global {
    .type-name-open {
      color: var(--eui-primary-pri-500);
    }
    .type-group-name {
      max-width: 150px;
      display: inline-block;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .type-name {
      display: flex;
      align-items: center;
      .font-size-2;
      .icon {
        margin-left: 4px;
      }
      .small {
        font-size: @icon-size-1;
      }
    }
    .eui-dropdown-menu{

    }
  }
}

.filter-group-wrapper, .multi-select-filter-group-wrapper {
  width: 216px;
  .font-size-2;
  :global {
    .filter-group-content {
      overflow-x: hidden;
      overflow-y: auto;
      max-height: 330px;
      margin-top: -4px;
      .ekb-drop-down-groupName {
        margin-top: 4px;
        margin-bottom: 12px;
        .font-weight-3;
      }
      .ekb-drop-down-item {
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: space-between;
        width: 46%;
        height: 32px;
        text-align: center;
        cursor: pointer;
        border-radius: 4px;
        margin-bottom: 12px;
        color: @color-black-1;
        padding: 4px 8px;
        background: rgba(78, 89, 105, 0.06);
        span {
          flex: 1;
          display: inline-block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          user-select: none;
        }
        &.selected {
          background: #ffffff;
          border: var(--eui-primary-pri-500) 1px solid;
        }
        &:hover {
          border: var(--eui-primary-pri-500) 1px solid;
        }
        .item-selected-tip {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 16px;
          height: 16px;
          border-bottom-right-radius: 4px;
          border-width: 8px;
          border-style: solid;
          border-color: #ffffff var(--eui-primary-pri-500) var(--eui-primary-pri-500) #ffffff;
          font-size: @icon-size-1;
          .icon {
            position: absolute;
            color: #ffffff;
            top: -2px;
            left: -2px;
          }
        }
        &:nth-child(2n) {
          margin-right: 8px;
        }
      }



      // 保留旧样式以向后兼容
      .filter-tag {
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: space-between;
        min-width: 80px;
        height: 32px;
        padding: 4px 8px;
        border-radius: 4px;
        border: 1px solid var(--eui-primary-pri-500);
        background: var(--eui-bg-body);
        color: var(--eui-text-title);
        cursor: pointer;
        user-select: none;

        span {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &:hover {
          opacity: 0.8;
        }

        &.filter-tag-selected {
          background: var(--eui-primary-pri-500);
          color: var(--eui-static-white);
        }

        .tag-check-icon {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 16px;
          height: 16px;
          border-bottom-right-radius: 4px;
          border-width: 8px;
          border-style: solid;
          border-color: #ffffff var(--eui-primary-pri-500) var(--eui-primary-pri-500) #ffffff;

          .icon {
            position: absolute;
            color: #ffffff;
            top: -2px;
            left: -2px;
            font-size: @icon-size-1;
          }
        }
      }
    }

    .ekb-drop-down-footer {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
      .btn {
        width: 80px;
        background-color: rgba(78, 89, 105, 0.06);
        margin-right: 16px;
      }
      > button {
        flex: 1;
      }
    }
  }
}

.bills-filter-dropdown {
  :global {
    .eui-dropdown-menu {
      max-height: 260px;
      width: 200px;
      overflow-y: auto;
    }
  }
}

.view-options {
  display: flex;
  align-items: center;
  .action {
    color: var(--eui-text-title);
    font: var(--eui-font-body-r1);
    &.action-open {
      color: var(--eui-primary-pri-500);
    }
    margin-left: 20px;
    &:first-child {
      margin-left: 0;
    }
    .icon {
      margin-left: 4px;
    }
  }
}

.multi-select-filter-group-wrapper {
  width: 100%;
  :global {
    .filter-group-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: 0;
      max-height: 372px;
      padding: 0 12px;
    }

    .multi-select-filter-footer {
      display: flex;
      justify-content: space-between;
      padding: 12px 12px 0;
      > button {
        flex-grow: 1;
      }
    }
  }
}

.filter-popover {
  :global {
    .eui-popover-inner {
      padding: 12px 0;
      width: 240px;
    }
  }
}