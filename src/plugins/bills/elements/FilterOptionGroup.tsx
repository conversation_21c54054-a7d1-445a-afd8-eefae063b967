import React from 'react'
import { Checkbox, Button } from '@hose/eui'
import { OutlinedTipsDone } from '@hose/eui-icons'
import styles from './FilterOptionGroup.module.less'
import classNames from 'classnames'

export interface FilterOption {
  type: string
  label: string
  isSelected: boolean
}

export interface FilterOptionGroupProps {
  /**
   * 筛选项标题
   */
  title: string
  /**
   * 选项列表
   */
  options: FilterOption[]
  /**
   * 选项变化回调
   */
  onChange: (optionType: string, isSelected: boolean) => void
}

/**
 * 可复用的筛选选项组件
 * 根据 Figma 设计实现：
 * - "全部"选项使用 Checkbox
 * - 其他选项使用带选中图标的按钮样式
 */
const FilterOptionGroup: React.FC<FilterOptionGroupProps> = (props) => {
  const { title, options, onChange } = props

  // 查找"全部"选项
  const allOption = options.find(option =>
    option.type === 'all' || option.type === 'ALL_DELEGATE'
  )

  // 其他选项
  const otherOptions = options.filter(option =>
    option.type !== 'all' && option.type !== 'ALL_DELEGATE'
  )

  /**
   * 处理选项点击
   */
  const handleOptionClick = (optionType: string, currentSelected: boolean) => {
    onChange(optionType, !currentSelected)
  }

    return (
    <div className={styles['filter-group']}>
      <div className={styles['filter-group-title']}>
        <span>{title}</span>
      </div>

      {/* 全部选项 - 使用 Checkbox，按照 Figma 设计 */}
      {allOption && (
        <div className={styles['checkbox-option-container']}>
          <Checkbox
            checked={allOption.isSelected}
            onChange={(e) => onChange(allOption.type, e.target.checked)}
          >
            <span className={styles['checkbox-label']}>{allOption.label}</span>
          </Checkbox>
        </div>
      )}

      {/* 其他选项 - 使用 Button 组件，按照 Figma 设计 */}
      <div className={styles['filter-option-tags']}>
        {otherOptions.map(option => (
          <div key={option.type} className={classNames(
            styles['filter-option-wrapper'],
            option.isSelected && styles['selected']
          )}>
            <Button
              category="secondary"
              className={styles['filter-option-button']}
              onClick={() => handleOptionClick(option.type, option.isSelected)}
              size='mini'
            >
              {option.label}
              {option.isSelected && (
                <>
                  <div className={styles['filter-option-check']} />
                  <OutlinedTipsDone className={styles['filter-option-check-icon']} fontSize={8} />
                </>
              )}
            </Button>

          </div>
        ))}
      </div>
    </div>
  )
}

export default FilterOptionGroup

/**
 * 使用示例:
 *
 * const options: FilterOption[] = [
 *   { type: 'all', label: '全部状态', isSelected: true },
 *   { type: 'draft', label: '待提交', isSelected: true },
 *   { type: 'approving', label: '审批中', isSelected: true },
 *   { type: 'rejected', label: '已驳回', isSelected: false },
 * ]
 *
 * <FilterOptionGroup
 *   title="提交状态"
 *   options={options}
 *   onChange={(optionType, isSelected) => {
 *     console.log(`Option ${optionType} is now ${isSelected ? 'selected' : 'unselected'}`)
 *   }}
 * />
 *
 * 特性（严格按照 Figma 设计）：
 * - "全部"选项自动使用 Checkbox 组件
 * - 其他选项使用 @hose/eui 的 Button 组件 (category="secondary")
 * - 所有按钮保持白色背景 + 蓝色边框样式
 * - 选中状态通过右下角蓝色三角形 + OutlinedTipsDone 图标表示
 * - 尺寸：104px 宽 x 32px 高
 */
