import React, { useEffect, useMemo, useState } from 'react'
import { Button } from '@hose/eui'
import { cloneDeep, pick } from 'lodash'
import styles from './BillFilter.module.less'
import FilterOptionGroup, { FilterOption } from './FilterOptionGroup'
import { app as api } from '@ekuaibao/whispered'

export interface MultiSelectOption {
  type: string
  label: string
  isSelected: boolean
}

export interface MultiSelectFilterState {
  [key: string]: MultiSelectOption[]
}

interface menuProps {
  type: string
  label: string
  children?: menuProps[]
}

interface MultiSelectFilterGroupProps {
  filterList: menuProps[]
  onConfirm: (filterTypes: MultiSelectFilterState) => void
  onMultiSelectChange?: (filterState: MultiSelectFilterState) => void
}

const getValueByFilterType = (filterState: MultiSelectFilterState, type: string) => {
  const value = filterState[type]
  const isAllOption = value.find(opt => opt.isSelected && (opt.type === 'all' || opt.type === 'ALL_DELEGATE'))
  if (isAllOption) {
    return [isAllOption.type]
  }
  return value
    .filter(opt => opt.isSelected && opt.type !== 'all' && opt.type !== 'ALL_DELEGATE')
    .map(opt => opt.type)
}

const getStateValue = (_multiSelectFilterState: any) => {
  // Convert multi-select state to compatible format for existing onChange
  return Object.keys(_multiSelectFilterState).map(filterType => ({
    key: filterType,
    value: getValueByFilterType(_multiSelectFilterState, filterType)
  }))
}

export const getCachedMultiSelectFilter = (): { key: string, value: string[] }[] => {
  const multiSelectUserInfoId = api.getState('@common')?.userinfo?.staff?.id + '-multiselect-filter' || 'bill-multiselect-filter'
  const cachedMultiSelectFilter = localStorage.getItem(multiSelectUserInfoId)
  if (cachedMultiSelectFilter) {
    try {
      const parsedFilter = JSON.parse(cachedMultiSelectFilter)
      return getStateValue(parsedFilter)
    } catch (error) {
      console.warn('Failed to parse cached multi-select filter:', error)
      return []
    }
  }
}

const getDefaultMultiSelectFilterState = (filterList: any) => {
  const initialState: MultiSelectFilterState = {}

  filterList.forEach(filterItem => {
    if (filterItem?.children?.length) {
      if (filterItem.type === 'entrust') {
        const isDelegateToMe = (type: string) => type === 'DELEGATE_TO_ME'
        // 默认 "DELEGATE_TO_ME"
        initialState[filterItem.type] = filterItem.children.map(child => ({
          type: child.type,
          label: child.label,
          isSelected: isDelegateToMe(child.type)
        }))
        return
      } else {
        // 默认全选
        initialState[filterItem.type] = filterItem.children.map(child => ({
          type: child.type,
          label: child.label,
          isSelected: true // Default to all selected
        }))
      }

    }
  })

  return initialState
}

/**
 * Multi-select filter group component that supports checkbox-based filtering
 */
const MultiSelectFilterGroup: React.FC<MultiSelectFilterGroupProps> = (props) => {
  const { filterList, onConfirm } = props

  // Multi-select filter state
  const [multiSelectFilterState, setMultiSelectFilterState] = useState<MultiSelectFilterState>(() => {
    if (!filterList.length) return {}
    return getDefaultMultiSelectFilterState(filterList)
  })

   /**
   * Check if confirm button should be disabled for multi-select filters
   */
   const isConfirmDisabled = useMemo(() => {
    if (!filterList.length) return false

    return filterList.some(filterItem => {
      const selectedOptions = multiSelectFilterState[filterItem.type]?.filter(opt =>
        opt.isSelected && opt.type !== 'all' && opt.type !== 'ALL_DELEGATE'
      ) || []
      return selectedOptions.length === 0
    })
  }, [filterList, multiSelectFilterState])

  useEffect(() => {
    const multiSelectUserInfoId = api.getState('@common')?.userinfo?.staff?.id + '-multiselect-filter' || 'bill-multiselect-filter'
    const cachedMultiSelectFilter = localStorage.getItem(multiSelectUserInfoId)
    if (cachedMultiSelectFilter) {
      try {
        const parsedFilter = JSON.parse(cachedMultiSelectFilter)
        setMultiSelectFilterState(parsedFilter)
      } catch (error) {
        console.warn('Failed to parse cached multi-select filter:', error)
      }
    }
  }, [])

  /**
   * Handle multi-select filter state change
   */
  const handleMultiSelectFilterChange = (newFilterState: MultiSelectFilterState) => {
    setMultiSelectFilterState(newFilterState)
  }

  /**
   * Reset multi-select filters to default state
   */
  const handleMultiSelectReset = () => {
    setMultiSelectFilterState(
      getDefaultMultiSelectFilterState(filterList)
    )
  }

  /**
  * Confirm multi-select filter changes
  */
  const handleMultiSelectConfirm = () => {

    // Call new multi-select callback if provided
    handleMultiSelectFilterChange?.(multiSelectFilterState)
    // Save to localStorage
    const userInfoId = api.getState('@common')?.userinfo?.staff?.id + '-multiselect-filter' || 'bill-multiselect-filter'
    localStorage.setItem(userInfoId, JSON.stringify(multiSelectFilterState))

    const filterTypes = getStateValue(multiSelectFilterState)
    // Call existing onChange for backward compatibility
    onConfirm(filterTypes as any)
  }

    /**
   * Handle option selection for a specific filter group
   */
  const handleFilterGroupOptionChange = (filterType: string) => {
    return (optionType: string, isChecked: boolean) => {
      const newFilterState = cloneDeep(multiSelectFilterState)
      const options = newFilterState[filterType] || []
      const option = options.find(opt => opt.type === optionType)

      if (option) {
        option.isSelected = isChecked
      }

      // Handle "All" option logic - all filter items have all option
      const allOption = options.find(opt => opt.type === 'all' || opt.type === 'ALL_DELEGATE')
      const otherOptions = options.filter(opt => opt.type !== 'all' && opt.type !== 'ALL_DELEGATE')

      if (optionType === 'all' || optionType === 'ALL_DELEGATE') {
        // When "All" is clicked, select/deselect all others
        otherOptions.forEach(opt => {
          opt.isSelected = isChecked
        })
      } else {
        // When individual option is clicked, update "All" status
        const areAllOthersSelected = otherOptions.every(opt => opt.isSelected)
        if (allOption) {
          allOption.isSelected = areAllOthersSelected
        }
      }

      handleMultiSelectFilterChange(newFilterState)
    }
  }

    return (
    <div className={styles['multi-select-filter-group-wrapper']}>
      <div className="filter-group-content">
        {filterList.map(filterItem => {
          if (!filterItem?.children?.length) return null

          // 转换为 FilterOption 格式
          const options: FilterOption[] = filterItem.children.map(child => ({
            type: child.type,
            label: child.label,
            isSelected: multiSelectFilterState[filterItem.type]?.find(opt => opt.type === child.type)?.isSelected || false
          }))

          return (
            <FilterOptionGroup
              key={filterItem.type}
              title={filterItem.label}
              options={options}
              onChange={handleFilterGroupOptionChange(filterItem.type)}
            />
          )
        })}
      </div>

      <div className="multi-select-filter-footer">
        <Button category="secondary" className="mr-8" onClick={handleMultiSelectReset}>
          {i18n.get('重置')}
        </Button>
        <Button
          category="primary"
          className="primary-btn"
          disabled={isConfirmDisabled}
          onClick={handleMultiSelectConfirm}
        >
          {i18n.get('确定')}
        </Button>
      </div>
    </div>
  )
}

export default MultiSelectFilterGroup
