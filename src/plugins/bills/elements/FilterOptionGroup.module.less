.filter-group-warpper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

// 筛选组容器 (按照 Figma gap-2 规范)
.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px; // gap-2
  width: 100%;
}

// 筛选组标题样式
.filter-group-title {
  font: var(--eui-text-title);
  color: var(--eui-text-title);
}

// 全部选项的容器样式 (严格按照 Figma 设计)
.checkbox-option-container {
  display: flex;
  gap: 8px; // gap-2
  height: 20px; // h-5
  align-items: center;
  margin-bottom: 0; // 无额外下边距

  // Checkbox 组件样式调整
  :global(.eui-checkbox) {
    margin: 0;

    .eui-checkbox-wrapper {
      align-items: center;
    }

    .eui-checkbox-input + .eui-checkbox-inner {
      width: 16px; // size-4
      height: 16px;
      border-radius: 2px; // rounded-sm
    }
  }

  // 文字样式
  .checkbox-label {
    color: var(--eui-text-title);
    font: var(--eui-font-body-r1);
    white-space: nowrap;
  }
}

.filter-option-tags {
  display: flex;
  flex-wrap: wrap;
  column-gap: 8px;
  row-gap: 12px;
}

// 使用 Button 组件的筛选选项 (严格按照 Figma 设计)
.filter-option-wrapper {
  position: relative;
  display: inline-block;
  width: calc(50% - 4px);

  &.selected {
    .filter-option-button {
      background: var(--eui-bg-body);
      border-color: var(--eui-primary-pri-500);
      overflow: hidden;
    }
  }
}

.filter-option-button {
  height: 32px;
  padding: 6px 12px; // py-1.5 px-3 (按照 Figma 设计)
  // 确保按钮保持白色背景和蓝色边框
  border: 1px solid transparent;
  border-radius: 6px;
  background-color: var(--eui-bg-body-overlay);
  color: var(--eui-text-title);
  font: var(--eui-font-body-r1);
  overflow: hidden;
  min-width: auto;
  width: 100%;
}

.filter-option-check-icon {
  position: absolute;
  right: 1px;
  bottom: 1px;
  color: var(--eui-bg-body);
}

.filter-option-check {
  position: absolute;
  right: -12px;
  bottom: -12px;
  width: 23px; // size-4
  height: 23px;
  pointer-events: none;
  transform: rotate(45deg);

  :global {
    .eui-icon {
      position: absolute;
      left: 0;
      top: 0;
    }
  }

  // 蓝色方形背景 (严格按照 Figma 设计: size-4)
  &::before {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    width: 23px;
    height: 23px;
    background: var(--eui-primary-pri-500);
    border-bottom-right-radius: 6px; // rounded-md 的右下角
  }
}
