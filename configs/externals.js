const fs = require('fs')
const path = require('path')

// 创建 moment externals 配置
function createMomentExternals() {
  // 创建基础配置
  const externals = {
    moment: 'lib__moment',
    'moment/moment': 'lib__moment', // 处理 import from "moment/moment"
    'moment/locale': 'lib__moment.locale',
    'moment/locale/zh-cn': 'lib__moment.locale["zh-cn"]'
  }

  return externals
}

/**
 * 创建 Lodash Externals 配置
 * 自动扫描 node_modules/lodash 目录，生成所有方法的 externals 配置
 */
function createLodashExternals(options = {}) {
  const {
    prefix = 'lodash.',
    includeCore = true,
    modulesDir = path.resolve(process.cwd(), 'node_modules/lodash')
  } = options

  // 基础配置
  const externals = {}
  if (includeCore) {
    externals['lodash/'] = 'lodash'
  }

  try {
    // 读取 lodash 目录下的所有文件
    const files = fs.readdirSync(modulesDir)

    // 过滤出 JS 文件并移除扩展名
    const methods = files
      .filter(file => file.endsWith('.js') && !file.startsWith('_'))
      .map(file => file.replace('.js', ''))

    // 为每个方法创建 externals 配置
    methods.forEach(method => {
      externals[`lodash/${method}`] = `${prefix}${method}`
      externals[`lodash.${method}`] = `${prefix}${method}`
    })
    return externals
  } catch (error) {
    console.warn('创建 Lodash Externals 失败:', error.message)
    return externals
  }
}

module.exports = config => {
  if (config && config.patch && config.patch.vendors) {
    config.patch.vendors(['dist/*/eui-icons.js', ['@hose/eui-icons']])

    // config.patch.vendors(['dist/*/eui-icons-menu.js', ['@hose/eui-icons']])
  }

  config.externals({
    '@hose/eui-icons': 'lib_eui_icons',
    ...createMomentExternals(),
    ...createLodashExternals({
      prefix: 'lib_lodash.'
    })
  })
}
